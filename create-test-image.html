<!DOCTYPE html>
<html>
<head>
    <title>Create Test Image</title>
</head>
<body>
    <h1>Создание тестового изображения</h1>
    <canvas id="canvas" width="300" height="200" style="border: 1px solid #000;"></canvas>
    <br><br>
    <button onclick="downloadImage()">Скачать тестовое изображение</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Создаем простое тестовое изображение
        ctx.fillStyle = '#0066cc';
        ctx.fillRect(0, 0, 300, 200);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Test Image', 150, 100);
        ctx.fillText('for Drag & Drop', 150, 130);
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test-image.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
