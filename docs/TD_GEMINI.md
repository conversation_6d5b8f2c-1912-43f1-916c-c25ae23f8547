Создание четкой технической документации и плана — ключ к успешной разработке в команде.

На основе предоставленных требований и нашего предыдущего обсуждения, вот план разработки, который можно использовать как руководство.

---

### **План разработки: GUI-конвертер медиафайлов**

**Версия документа:** 1.0
**Дата:** 01.08.2025 г.
**Автор:** Gemini AI

#### **Краткий обзор**

Этот документ описывает технологический стек и техническую архитектуру для разработки настольного приложения "Media Converter". Цель проекта — создать минималистичный, быстрый и эстетически приятный инструмент для конвертации медиафайлов, с первоначальным фокусом на конвертации изображений в JPEG. Приложение должно быть интуитивно понятным, поддерживать пакетную обработку и обеспечивать качественный пользовательский опыт, вдохновленный дизайном существующих утилит для macOS (например, Permute).

---

### **1. Технологический стек (Technology Stack)**

Выбор технологий обусловлен требованиями к производительности, кроссплатформенности и скорости разработки.

| Компонент | Технология | Обоснование |
| :--- | :--- | :--- |
| **Основной фреймворк** | **Tauri (v1.6+)** | Позволяет создавать легковесные, безопасные и кроссплатформенные настольные приложения с использованием веб-технологий для UI и Rust для бэкенда. Обеспечивает прямую интеграцию с ОС для таких функций, как drag-n-drop и прозрачность окна. |
| **Бэкенд (Core Logic)** | **Rust (2021 Edition)** | Идеально подходит для ресурсоемких задач, таких как обработка изображений, благодаря своей производительности и безопасности памяти. Экосистема Cargo предоставляет доступ к мощным библиотекам (`crates`). |
| **Фронтенд (UI)** | **React JS (v18+) с TypeScript** | Обеспечивает быструю разработку реактивного пользовательского интерфейса с помощью компонентного подхода. TypeScript добавляет строгую типизацию, что снижает количество ошибок и упрощает поддержку кода. |
| **Обработка изображений** | **`image` crate (Rust)** | Мощная и популярная библиотека в экосистеме Rust для чтения, записи и простой манипуляции с различными форматами изображений. Идеально подходит для нашей задачи конвертации. |
| **Связь Frontend/Backend** | **`@tauri-apps/api`** | Официальная JS/TS-библиотека Tauri для вызова Rust-команд из фронтенда (`invoke`) и прослушивания событий от бэкенда (`listen`). |
| **Стилизация** | **CSS (с CSS Variables)** | Для MVP будет использован чистый CSS с переменными для легкой настройки тем. Это обеспечивает простоту и хорошую производительность. В будущем возможен переход на CSS Modules или Styled Components для лучшей инкапсуляции. |
| **Сборка и управление пакетами** | **NPM / Cargo** | `npm` для управления зависимостями фронтенда и запуска скриптов. `Cargo` — стандартный менеджер пакетов и система сборки для Rust. |

---

### **2. Техническая документация (Technical Documentation)**

#### **2.1. Архитектура приложения**

Приложение следует стандартной архитектуре Tauri:
1.  **Процесс Core (Rust):** Основной процесс, написанный на Rust. Он управляет окном приложения, обрабатывает системные события и выполняет всю "тяжелую" логику. В нашем случае это конвертация файлов.
2.  **Процесс WebView (React):** Пользовательский интерфейс, который рендерится в системном WebView. Он отвечает за отображение состояния, взаимодействие с пользователем и отправку команд в процесс Core.
3.  **Канал связи (IPC):** Tauri организует асинхронный канал связи между WebView и Core. Фронтенд вызывает Rust-функции через `invoke`, передавая данные в формате JSON. Rust возвращает `Result`, который во фронтенде преобразуется в `Promise`.

#### **2.2. Пользовательский интерфейс (UI/UX)**

UI должен быть чистым, минималистичным и соответствовать предоставленным макетам.

*   **Основное окно:** Одно окно без вкладок. Фон полупрозрачный с эффектом размытия (`backdrop-filter`).
*   **Состояние 1: Пустой список (макет 1)**
    *   Отображается по центру окна.
    *   Содержит иконку и текст "Перетащите файлы для конвертации".
    *   Вся область окна является зоной для перетаскивания файлов (drop zone).
*   **Состояние 2: Файлы добавлены (макет 2)**
    *   Отображается вертикальный список добавленных файлов.
    *   Каждый элемент списка (`FileItem`) содержит:
        *   **Превью:** Заглушка-иконка (в будущем — миниатюра изображения).
        *   **Имя файла:** `image.png`.
        *   **Статус:** "Ожидание", "Конвертация", "Завершено", "Ошибка".
        *   **Кнопки управления:** "Конвертировать" (для одиночного файла), "Удалить".
    *   Внизу окна находятся общие кнопки: "Конвертировать все", "Очистить все".
*   **Состояние 3: Конвертация завершена (макет 3)**
    *   В элементе `FileItem` статус меняется на "Завершено".
    *   Иконка-заглушка меняется на иконку успеха (галочка).
    *   Кнопка "Конвертировать" исчезает.

#### **2.3. Логика Бэкенда (Rust)**

Бэкенд будет предоставлять одну основную команду для фронтенда.

**Команда:** `convert_to_jpeg`

*   **Сигнатура:** `#[tauri::command] async fn convert_to_jpeg(file_path: String) -> Result<String, String>`
*   **Входные данные:** `file_path: String` — полный путь к файлу, который нужно конвертировать.
*   **Логика выполнения:**
    1.  Принять `file_path`.
    2.  Проверить существование файла.
    3.  Открыть изображение с помощью библиотеки `image::open()`.
    4.  Сформировать новый путь для сохранения файла, заменив расширение на `.jpeg`.
    5.  Сохранить изображение в формате JPEG с помощью `image::save()`.
*   **Возвращаемое значение:**
    *   **`Ok(String)`:** В случае успеха возвращает имя нового созданного файла (например, `image.jpeg`).
    *   **`Err(String)`:** В случае ошибки возвращает понятное сообщение (например, "Не удалось открыть файл" или "Формат не поддерживается").

#### **2.4. Логика Фронтенда (React)**

**Состояние (State Management):**
Основное состояние будет храниться в `App` компоненте с помощью `useState`.
`const [files, setFiles] = useState<FileItem[]>([]);`

**Интерфейс `FileItem`:**
```typescript
interface FileItem {
  id: number;       // Уникальный идентификатор (e.g., Date.now())
  path: string;     // Полный путь к исходному файлу
  name: string;     // Отображаемое имя файла
  status: 'pending' | 'converting' | 'done' | 'error';
  error?: string;   // Сообщение об ошибке, если status === 'error'
}
```

**Обработка событий:**

1.  **`tauri://file-drop`:**
    *   Использовать хук `useEffect` для подписки на это событие при монтировании компонента.
    *   При получении события (массив путей к файлам) создать новые объекты `FileItem` и добавить их в состояние `files`.
2.  **Нажатие "Конвертировать" (для одного файла):**
    *   Найти файл в состоянии по `id`.
    *   Изменить его статус на `converting`.
    *   Вызвать `await invoke('convert_to_jpeg', { filePath: file.path })`.
    *   В `try/catch` блоке обработать результат:
        *   **`try` (успех):** Изменить статус на `done` и обновить `name` на новое имя файла, возвращенное из Rust.
        *   **`catch` (ошибка):** Изменить статус на `error` и записать сообщение об ошибке в поле `error`.
3.  **Нажатие "Удалить":**
    *   Отфильтровать массив `files`, удалив элемент с соответствующим `id`.
4.  **Нажатие "Конвертировать все":**
    *   Проитерироваться по всем файлам со статусом `pending` и вызвать для каждого логику конвертации одного файла.
5.  **Нажатие "Очистить все":**
    *   Вызвать `setFiles([])`.

#### **2.5. План разработки (Roadmap)**

**Этап 1: MVP (Минимально жизнеспособный продукт)**
*   [✓] Инициализация проекта Tauri + React + TS.
*   [✓] Реализация Rust-команды для конвертации любого изображения в JPEG.
*   [✓] Реализация UI с поддержкой drag-n-drop для добавления файлов.
*   [✓] Функциональность списка: добавление, удаление (по одному, все), конвертация (по одному, все).
*   [✓] Стилизация UI в соответствии с макетами, включая полупрозрачность.
*   [✓] Сборка и тестирование приложения под macOS.

**Этап 2: Улучшения и расширение функционала**
*   [_] **Реальные превью:** Вместо иконок генерировать и отображать миниатюры изображений.
*   [_] **Настройки конвертации:** Добавить возможность выбора качества JPEG.
*   [_] **Поддержка других форматов:** Добавить выбор целевого формата (PNG, WebP).
*   [_] **Улучшенный фидбек:** Отображение прогресс-бара для конвертации, более детальные ошибки.

**Этап 3: Кроссплатформенность и будущие возможности**
*   [_] Тестирование и сборка под Windows и Linux.
*   [_] **Поддержка видео:** Интеграция `ffmpeg` для конвертации видеофайлов.
*   [_] Локализация интерфейса.