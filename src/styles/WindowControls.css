.window-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-app-region: no-drag;
}

.window-controls {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 8px;
  -webkit-app-region: no-drag;
}

.window-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  text-align: center;
  user-select: none;
  -webkit-app-region: no-drag;
}

.window-control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.window-control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.window-control-btn:active {
  transform: scale(0.95);
}

.window-control-btn.close {
  background: #ff5f57;
  color: #fff;
}

.window-control-btn.close:hover {
  background: #ff4136;
}

.window-control-btn.minimize {
  background: #ffbd2e;
  color: #fff;
}

.window-control-btn.minimize:hover {
  background: #ffaa00;
}

.window-control-btn.maximize {
  background: #28ca42;
  color: #fff;
}

.window-control-btn.maximize:hover {
  background: #20a034;
}
