# 🎨 Permute Converter

> Элегантный конвертер изображений для macOS с минималистичным дизайном

![macOS](https://img.shields.io/badge/macOS-11%2B-blue?style=flat-square&logo=apple)
![Tauri](https://img.shields.io/badge/Tauri-2.0-orange?style=flat-square&logo=tauri)
![React](https://img.shields.io/badge/React-18-61dafb?style=flat-square&logo=react)
![Rust](https://img.shields.io/badge/Rust-2021-orange?style=flat-square&logo=rust)

## ✨ Что это такое?

Permute Converter — это быстрый и красивый конвертер изображений, созданный специально для macOS. Просто перетащите файлы в окно приложения, и они мгновенно превратятся в оптимизированные JPEG! 

### 🚀 Особенности

- **🎯 Drag & Drop** — Просто перетащите изображения в окно
- **⚡ Молниеносная скорость** — Конвертация на Rust = максимальная производительность  
- **🎨 Нативный дизайн** — Полупрозрачный интерфейс в стиле macOS
- **📦 Пакетная обработка** — Конвертируйте сразу несколько файлов
- **🔍 Превью** — Миниатюры изображений прямо в списке
- **💾 Легковесность** — Размер приложения меньше 10MB

### 📸 Поддерживаемые форматы

**Входные:** PNG, BMP, GIF, TIFF, WEBP  
**Выходной:** JPEG (оптимизированный)

## 🛠 Установка и запуск

### Для пользователей

1. Скачайте последний релиз из [Releases](../../releases)
2. Перетащите `.app` файл в папку Applications
3. Запустите и наслаждайтесь! 🎉

### Для разработчиков

```bash
# Клонируем репозиторий
git clone https://github.com/your-username/permute-converter.git
cd permute-converter

# Устанавливаем зависимости
npm install

# Запускаем в режиме разработки
npm run tauri dev

# Собираем релиз
npm run tauri build
```

### 📋 Требования

- **macOS 11 Big Sur** или новее
- **Node.js 18+** (для разработки)
- **Rust 1.70+** (для разработки)

## 🎮 Как пользоваться

1. **Перетащите** изображения в окно приложения
2. **Нажмите** "Конвертировать все" или конвертируйте по одному
3. **Готово!** JPEG файлы появятся рядом с оригиналами

![Demo GIF placeholder - добавьте сюда гифку с демонстрацией]

## 🏗 Архитектура

```
Frontend (React + TypeScript)
    ↕️ Tauri IPC
Backend (Rust + image crate)
    ↕️ System APIs
macOS (File System + UI)
```

**Почему Tauri?**
- 🔒 Безопасность из коробки
- ⚡ Производительность нативных приложений
- 🎨 Современный веб-интерфейс
- 📦 Крошечный размер бандла

## 🤝 Участие в разработке

Мы рады вашим идеям и улучшениям! 

1. Форкните репозиторий
2. Создайте ветку для фичи (`git checkout -b feature/amazing-feature`)
3. Закоммитьте изменения (`git commit -m 'Add amazing feature'`)
4. Запушьте в ветку (`git push origin feature/amazing-feature`)
5. Откройте Pull Request

### 🐛 Нашли баг?

Создайте [Issue](../../issues) с подробным описанием проблемы.

## 🗺 Roadmap

- [ ] 🎛 Настройки качества JPEG
- [ ] 🖼 Поддержка других форматов (PNG, WebP)
- [ ] 📱 Версия для iOS/iPadOS
- [ ] 🌍 Локализация на другие языки
- [ ] 🎬 Конвертация видео

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. Подробности в файле [LICENSE](LICENSE).

## 💝 Благодарности

- [Tauri](https://tauri.app/) за потрясающий фреймворк
- [image-rs](https://github.com/image-rs/image) за мощную библиотеку обработки изображений
- Сообществу Rust и React за вдохновение

---

<div align="center">
  <p>Сделано с ❤️ для macOS</p>
  <p>
    <a href="https://github.com/your-username">GitHub</a> •
    <a href="mailto:<EMAIL>">Email</a> •
    <a href="https://twitter.com/your-handle">Twitter</a>
  </p>
</div>
