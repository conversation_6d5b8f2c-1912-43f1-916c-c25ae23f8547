/* File List */
.file-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.file-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
}

.file-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  min-height: 80px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.file-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--bg-image, none);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.25;
  z-index: 0;
  image-rendering: auto;
  image-rendering: -webkit-optimize-contrast;
}

.file-item .preview {
  width: 50px;
  height: 50px;
  background-color: var(--icon-color);
  border-radius: 4px;
  margin-right: 12px;
  /* Иконка файла */
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
  mask-size: 60%;
  mask-repeat: no-repeat;
  mask-position: center;
}

/* Статусы - перенесены в конец файла */

.file-item .info {
  flex-grow: 1;
}

.file-item .name {
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item .status {
  font-size: 12px;
  color: #a0a0a0;
  margin: 4px 0 0 0;
}

.file-item .actions {
  display: flex;
  align-items: center;
}

.file-item-overlay {
  background-color: rgba(20, 22, 25, 0.05);
  padding: 12px;
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.converted-name {
  font-size: 11px;
  color: #4CAF50;
  margin: 2px 0 0 0;
  font-style: italic;
}

button {
  background-color: #333;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #444;
}

button:disabled {
  background-color: #222;
  color: #666;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: #222;
}

.remove-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #888;
  cursor: pointer;
  margin-left: 10px;
}
