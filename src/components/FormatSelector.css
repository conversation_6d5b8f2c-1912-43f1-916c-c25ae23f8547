.format-selector {
  position: fixed;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 4px;
  z-index: 1000;
  -webkit-app-region: no-drag;
}

.format-btn {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--text-color);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.format-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* JPEG - Красный */
.format-selector .format-btn.active[data-format="jpeg"],
button.format-btn.active[data-format="jpeg"] {
  background: rgba(220, 53, 69, 0.9) !important;
  background-color: rgba(220, 53, 69, 0.9) !important;
  border-color: rgba(220, 53, 69, 1) !important;
  color: white !important;
  box-shadow: 0 0 15px rgba(220, 53, 69, 0.6) !important;
}

/* WEBP - Желтый */
.format-selector .format-btn.active[data-format="webp"],
button.format-btn.active[data-format="webp"] {
  background: rgba(255, 193, 7, 0.9) !important;
  background-color: rgba(255, 193, 7, 0.9) !important;
  border-color: rgba(255, 193, 7, 1) !important;
  color: black !important;
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.6) !important;
}

/* PNG - Синий */
.format-selector .format-btn.active[data-format="png"],
button.format-btn.active[data-format="png"] {
  background: rgba(0, 123, 255, 0.9) !important;
  background-color: rgba(0, 123, 255, 0.9) !important;
  border-color: rgba(0, 123, 255, 1) !important;
  color: white !important;
  box-shadow: 0 0 15px rgba(0, 123, 255, 0.6) !important;
}

.format-selector .format-btn.active,
button.format-btn.active {
  transform: scale(1.1) !important;
}