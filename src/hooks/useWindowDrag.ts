import { useEffect } from "react";
import { getCurrentWindow } from "@tauri-apps/api/window";

export const useWindowDrag = () => {
  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      // Проверяем, что клик не по интерактивным элементам
      const target = e.target as HTMLElement;
      const isInteractive = target.closest(
        'button, input, select, textarea, a, [role="button"], .file-item, .controls, .window-controls'
      );

      if (!isInteractive) {
        const window = getCurrentWindow();
        window.startDragging().catch(console.error);
      }
    };

    document.addEventListener("mousedown", handleMouseDown);

    return () => {
      document.removeEventListener("mousedown", handleMouseDown);
    };
  }, []);
};
