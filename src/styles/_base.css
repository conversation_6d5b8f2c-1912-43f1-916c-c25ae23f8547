body {
  background: transparent;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: var(--text-color);
  overflow: hidden;
  /* Отключаем стандартное поведение drag & drop */
  -webkit-user-drag: none;
  -webkit-app-region: no-drag;
}

.container {
  background: var(--dynamic-background-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  /* Убираем границу чтобы не было линии между шапкой и контентом */
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* Делаем весь контейнер перемещаемым */
  -webkit-app-region: drag;
  position: relative;
  /* Добавляем отступ сверху для имитации шапки */
  padding-top: 56px;
  transition: background 0.3s ease;
}

/* Отключаем перемещение для интерактивных элементов */
.container button,
.container input,
.container select,
.container textarea,
.container a,
.container [role="button"],
.container .file-item,
.container .controls,
.container .window-controls {
  -webkit-app-region: no-drag;
}
