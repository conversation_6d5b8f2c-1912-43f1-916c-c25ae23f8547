// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::path::PathBuf;
use tauri::Manager;
use image::{ImageFormat, DynamicImage};
use std::io::Cursor;
use base64::{Engine as _, engine::general_purpose};

// Проверяем, является ли файл изображением
#[tauri::command]
async fn is_image_file(file_path: String) -> Result<bool, String> {
    println!("🔍 is_image_file вызвана для: {}", file_path);
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        println!("❌ Файл не существует: {}", file_path);
        return Ok(false);
    }

    // Проверяем расширение файла
    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        println!("📄 Расширение файла: {}", ext);
        match ext.as_str() {
            "jpg" | "jpeg" | "png" | "gif" | "bmp" | "tiff" | "webp" => {
                println!("✅ Файл является изображением");
                Ok(true)
            },
            _ => {
                println!("❌ Файл не является изображением");
                Ok(false)
            },
        }
    } else {
        println!("❌ У файла нет расширения");
        Ok(false)
    }
}

// Получаем предпросмотр изображения в формате base64
#[tauri::command]
async fn get_image_preview(file_path: String) -> Result<String, String> {
    println!("🎨 get_image_preview вызвана для: {}", file_path);
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        println!("❌ Файл не найден: {}", file_path);
        return Err("Файл не найден".into());
    }

    println!("📖 Открываем изображение...");
    let img = match image::open(&path) {
        Ok(img) => {
            println!("✅ Изображение успешно открыто");
            img
        },
        Err(e) => {
            println!("❌ Ошибка открытия изображения: {}", e);
            return Err(format!("Не удалось открыть изображение: {}", e));
        },
    };

    println!("🖼️ Создаем миниатюру...");
    // Создаем миниатюру 100x100
    let thumbnail = img.thumbnail(100, 100);

    println!("💾 Конвертируем в base64...");
    // Конвертируем в JPEG и кодируем в base64
    let mut buffer = Vec::new();
    let mut cursor = Cursor::new(&mut buffer);

    match thumbnail.write_to(&mut cursor, ImageFormat::Jpeg) {
        Ok(_) => {
            let base64_string = general_purpose::STANDARD.encode(&buffer);
            println!("✅ Предпросмотр создан успешно");
            Ok(format!("data:image/jpeg;base64,{}", base64_string))
        },
        Err(e) => {
            println!("❌ Ошибка создания предпросмотра: {}", e);
            Err(format!("Не удалось создать предпросмотр: {}", e))
        },
    }
}

// Открываем диалог выбора файлов
#[tauri::command]
async fn open_file_dialog(app: tauri::AppHandle) -> Result<Vec<String>, String> {
    use tauri_plugin_dialog::DialogExt;
    
    println!("🗂️ Открываем диалог выбора файлов...");
    
    let files = app.dialog()
        .file()
        .add_filter("Изображения", &["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"])
        .set_title("Выберите изображения для конвертации")
        .blocking_pick_files();
    
    match files {
        Some(paths) => {
            let file_paths: Vec<String> = paths.iter()
                .filter_map(|p| p.as_path())
                .map(|path| path.to_string_lossy().to_string())
                .collect();
            println!("✅ Выбрано файлов: {}", file_paths.len());
            Ok(file_paths)
        },
        None => {
            println!("❌ Диалог отменен пользователем");
            Ok(vec![])
        }
    }
}

// Конвертируем изображение в JPEG
#[tauri::command]
async fn convert_to_jpeg(_app: tauri::AppHandle, file_path: String) -> Result<String, String> {
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Err("Файл не найден".into());
    }

    let img = match image::open(&path) {
        Ok(img) => img,
        Err(e) => return Err(format!("Не удалось открыть изображение: {}", e)),
    };

    let mut new_path = path.clone();
    new_path.set_extension("jpeg");
    let new_file_name = new_path.file_name().unwrap().to_str().unwrap().to_string();

    // Конвертируем в RGB если нужно (для JPEG)
    let rgb_img = img.to_rgb8();
    let dynamic_img = DynamicImage::ImageRgb8(rgb_img);

    match dynamic_img.save_with_format(&new_path, ImageFormat::Jpeg) {
        Ok(_) => Ok(new_file_name),
        Err(e) => Err(format!("Не удалось сохранить JPEG: {}", e)),
    }
}

fn main() {
    println!("🚀 Запуск Tauri приложения...");
    
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            println!("🔧 Настройка приложения...");

            // Получаем главное окно
            let window = app.get_webview_window("main").unwrap();
            println!("🪟 Главное окно получено");

            // Применяем специфичные для macOS настройки для красивой прозрачности
            #[cfg(target_os = "macos")]
            {
                println!("🍎 Применяем настройки для macOS...");
                use tauri::TitleBarStyle;
                window.set_title_bar_style(TitleBarStyle::Transparent).ok();

                // Включаем vibrancy эффект для macOS
                use window_vibrancy::{apply_vibrancy, NSVisualEffectMaterial};
                let result = apply_vibrancy(&window, NSVisualEffectMaterial::HudWindow, None, None);
                match result {
                    Ok(_) => println!("✅ Vibrancy эффект применен"),
                    Err(e) => println!("❌ Ошибка применения vibrancy: {:?}", e),
                }
            }

            // Настраиваем окно для drag & drop
            println!("🎯 Настраиваем drag & drop...");

            // Устанавливаем фокус на окно
            if let Err(e) = window.set_focus() {
                println!("⚠️ Не удалось установить фокус: {:?}", e);
            } else {
                println!("✅ Фокус установлен");
            }

            // Проверяем, что drag & drop включен по умолчанию в Tauri 2.0
            println!("🔧 В Tauri 2.0 drag & drop должен работать автоматически");

            // Добавляем дополнительную отладочную информацию
            println!("🔍 Проверяем настройки окна...");
            println!("📋 Окно готово к приему drag & drop событий");

            // Открываем DevTools для отладки в dev режиме
            #[cfg(debug_assertions)]
            {
                window.open_devtools();
                println!("🔧 DevTools открыты для отладки");
            }

            println!("🔧 Окно настроено для drag & drop");
            println!("✅ Настройка завершена");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            convert_to_jpeg,
            is_image_file,
            get_image_preview,
            open_file_dialog
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}