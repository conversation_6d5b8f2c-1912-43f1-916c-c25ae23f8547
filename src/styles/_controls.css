/* Панель управления */
.controls {
  padding: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.controls .add-more-btn {
  color: var(--text-color);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid;
}

/* Цвета для кнопки "Добавить еще" */
.controls .add-more-btn.format-jpeg {
  background: rgba(220, 53, 69, 0.2) !important;
  border-color: rgba(220, 53, 69, 0.5) !important;
}

.controls .add-more-btn.format-jpeg:hover {
  background: rgba(220, 53, 69, 0.3) !important;
  border-color: rgba(220, 53, 69, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.controls .add-more-btn.format-webp {
  background: rgba(255, 193, 7, 0.2) !important;
  border-color: rgba(255, 193, 7, 0.5) !important;
}

.controls .add-more-btn.format-webp:hover {
  background: rgba(255, 193, 7, 0.3) !important;
  border-color: rgba(255, 193, 7, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.controls .add-more-btn.format-png {
  background: rgba(0, 123, 255, 0.2) !important;
  border-color: rgba(0, 123, 255, 0.5) !important;
}

.controls .add-more-btn.format-png:hover {
  background: rgba(0, 123, 255, 0.3) !important;
  border-color: rgba(0, 123, 255, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.controls .right-buttons {
  display: flex;
  gap: 12px;
}

/* Стили для кнопок с адаптивными цветами */
.convert-all-btn,
.clear-all-btn {
  color: var(--text-color);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid;
}

/* Цвета для JPEG формата */
.controls .convert-all-btn.format-jpeg,
.controls .clear-all-btn.format-jpeg {
  background: rgba(220, 53, 69, 0.2) !important;
  border-color: rgba(220, 53, 69, 0.5) !important;
}

.controls .convert-all-btn.format-jpeg:hover,
.controls .clear-all-btn.format-jpeg:hover {
  background: rgba(220, 53, 69, 0.3) !important;
  border-color: rgba(220, 53, 69, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* Цвета для WebP формата */
.controls .convert-all-btn.format-webp,
.controls .clear-all-btn.format-webp {
  background: rgba(255, 193, 7, 0.2) !important;
  border-color: rgba(255, 193, 7, 0.5) !important;
}

.controls .convert-all-btn.format-webp:hover,
.controls .clear-all-btn.format-webp:hover {
  background: rgba(255, 193, 7, 0.3) !important;
  border-color: rgba(255, 193, 7, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* Цвета для PNG формата */
.controls .convert-all-btn.format-png,
.controls .clear-all-btn.format-png {
  background: rgba(0, 123, 255, 0.2) !important;
  border-color: rgba(0, 123, 255, 0.5) !important;
}

.controls .convert-all-btn.format-png:hover,
.controls .clear-all-btn.format-png:hover {
  background: rgba(0, 123, 255, 0.3) !important;
  border-color: rgba(0, 123, 255, 0.8) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Состояние disabled */
.convert-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.convert-all-btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}
