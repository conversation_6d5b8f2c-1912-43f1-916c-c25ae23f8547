import { getCurrentWindow } from "@tauri-apps/api/window";
import "../styles/WindowControls.css";

export const WindowControls = () => {
  const handleMinimize = async () => {
    const window = getCurrentWindow();
    await window.minimize();
  };

  const handleMaximize = async () => {
    const window = getCurrentWindow();
    await window.toggleMaximize();
  };

  const handleClose = async () => {
    const window = getCurrentWindow();
    await window.close();
  };

  return (
    <div className="window-header">
      <div className="window-controls">
        <button
          className="window-control-btn close"
          onClick={handleClose}
          title="Закрыть"
        >
          ×
        </button>
        <button
          className="window-control-btn minimize"
          onClick={handleMinimize}
          title="Свернуть"
        >
          −
        </button>
        <button
          className="window-control-btn maximize"
          onClick={handleMaximize}
          title="Развернуть"
        >
          □
        </button>
      </div>
      <div className="window-title">Permute Converter</div>
    </div>
  );
};
