# Структура проекта

Этот проект был рефакторен для улучшения читаемости и поддерживаемости кода.

## Структура файлов

```
src/
├── components/           # React компоненты
│   ├── EmptyState.tsx   # Компонент пустого состояния
│   ├── FileItem.tsx     # Компонент отдельного файла
│   ├── FileList.tsx     # Компонент списка файлов
│   └── index.ts         # Экспорты компонентов
├── hooks/               # Пользовательские хуки
│   ├── useDragAndDrop.ts    # Хук для drag & drop функциональности
│   ├── useFileProcessing.ts # Хук для обработки файлов
│   └── index.ts         # Экспорты хуков
├── types/               # TypeScript типы
│   └── index.ts         # Определения типов
├── utils/               # Утилиты
│   └── debug.ts         # Утилиты для отладки
├── App.tsx              # Главный компонент приложения
├── App.css              # Стили приложения
└── main.tsx             # Точка входа
```

## Компоненты

### EmptyState
Отображает состояние когда нет загруженных файлов, с кнопкой для выбора файлов.

### FileItem
Представляет отдельный файл в списке с превью, статусом и действиями.

### FileList
Контейнер для списка файлов с drag & drop функциональностью и элементами управления.

## Хуки

### useFileProcessing
Управляет состоянием файлов и операциями:
- Добавление файлов
- Конвертация файлов
- Удаление файлов
- Выбор файлов через диалог

### useDragAndDrop
Обрабатывает drag & drop функциональность:
- Слушатели событий Tauri
- Визуальная обратная связь
- Обработка перетаскивания файлов

## Типы

### FileItem
Основной тип для представления файла в приложении с полями:
- id, path, name, status, error, preview

## Утилиты

### debug.ts
Централизованная система отладки с настраиваемым режимом DEBUG_MODE.