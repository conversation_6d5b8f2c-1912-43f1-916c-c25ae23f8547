.format-selector {
  position: fixed;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 4px;
  z-index: 1000;
  -webkit-app-region: no-drag;
}

.format-btn {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--text-color);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.format-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.format-btn.active[data-format="jpeg"] {
  background: rgba(220, 53, 69, 0.8);
  border-color: rgba(220, 53, 69, 1);
  color: white;
}

.format-btn.active[data-format="webp"] {
  background: rgba(255, 193, 7, 0.8);
  border-color: rgba(255, 193, 7, 1);
  color: white;
}

.format-btn.active[data-format="png"] {
  background: rgba(0, 123, 255, 0.8);
  border-color: rgba(0, 123, 255, 1);
  color: white;
}

.format-btn.active {
  transform: scale(1.1);
}