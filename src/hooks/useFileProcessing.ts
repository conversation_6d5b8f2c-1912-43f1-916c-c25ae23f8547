import { useState, useRef, useCallback } from "react";
import { invoke } from "@tauri-apps/api/core";
import { FileItem } from "../types";
import { ConversionFormat } from "../types/format";
import { debugLog } from "../utils/debug";

export const useFileProcessing = (selectedFormat: ConversionFormat) => {
  const [files, setFiles] = useState<FileItem[]>([]);

  // Реф для отслеживания времени последнего перетаскивания для дебаунса
  const lastProcessedDropTimeRef = useRef(0);
  const DEBOUNCE_INTERVAL = 50; // миллисекунды

  // Реф для отслеживания активных асинхронных операций
  const activeOperationsRef = useRef(new Set<string>());

  // Счетчик для уникальных ID
  const idCounterRef = useRef(0);

  // Поддерживаемые расширения изображений
  const imageExtensions = new Set([
    "jpg",
    "jpeg",
    "png",
    "gif",
    "bmp",
    "tiff",
    "webp",
  ]);

  // Быстрая проверка по расширению файла
  const isLikelyImage = (filePath: string): boolean => {
    const extension = filePath.split(".").pop()?.toLowerCase();
    return extension ? imageExtensions.has(extension) : false;
  };

  // Функция для обработки файлов, перетащенных или выбранных
  const processDroppedFiles = useCallback(
    async (paths: string[]) => {
      const currentTime = Date.now();
      if (currentTime - lastProcessedDropTimeRef.current < DEBOUNCE_INTERVAL) {
        debugLog(
          "⏱️ Дебаунс: Пропускаем обработку, слишком быстро после предыдущей."
        );
        return;
      }
      lastProcessedDropTimeRef.current = currentTime;

      debugLog(`🚀 Начинаем обработку ${paths.length} файлов...`);

      // Используем функциональное обновление для получения актуального состояния
      setFiles((currentFiles) => {
        const currentFilePaths = new Set(currentFiles.map((f) => f.path));
        const newFiles: FileItem[] = [];
        const filesToProcess: string[] = [];

        // МГНОВЕННО добавляем все файлы, которые похожи на изображения
        paths.forEach((path) => {
          // Проверяем, не добавлен ли файл уже
          if (currentFilePaths.has(path)) {
            debugLog(`⚠️ Файл уже добавлен: ${path}`);
            return;
          }

          const name = path.split(/[\\/]/).pop() || "unknown";

          // Быстрая проверка по расширению
          if (isLikelyImage(path)) {
            const newFile: FileItem = {
              id: ++idCounterRef.current, // Уникальный инкрементальный ID
              path,
              originalName: name,
              currentName: name,
              status: "validating", // Файл проверяется в фоне
              preview: undefined, // Предпросмотр загрузится позже
            };

            newFiles.push(newFile);
            filesToProcess.push(path);
            debugLog(`⚡ Файл добавлен мгновенно: ${name}`);
          } else {
            debugLog(`⚠️ Файл ${name} не похож на изображение (расширение)`);
          }
        });

        debugLog(`✅ Добавлено ${newFiles.length} файлов мгновенно!`);

        // Асинхронно обрабатываем файлы
        filesToProcess.forEach(async (path) => {
          // Проверяем, не была ли операция отменена
          if (!activeOperationsRef.current.has(path)) {
            activeOperationsRef.current.add(path);
          }

          try {
            // Проверяем, что операция все еще активна
            if (!activeOperationsRef.current.has(path)) {
              debugLog(`🚫 Операция для ${path} была отменена`);
              return;
            }

            // Сначала проверяем, действительно ли это изображение с таймаутом
            const isActualImage: boolean = await Promise.race([
              invoke<boolean>("is_image_file", { filePath: path }),
              new Promise<boolean>((_, reject) =>
                setTimeout(() => reject(new Error("Timeout")), 5000)
              ),
            ]);

            if (!activeOperationsRef.current.has(path)) {
              debugLog(`🚫 Операция для ${path} была отменена после проверки`);
              return;
            }

            if (!isActualImage) {
              // Удаляем файл, если он не является изображением
              setFiles((prevFiles) =>
                prevFiles.filter((file) => file.path !== path)
              );
              debugLog(`❌ Файл ${path} не является изображением, удален`);
              activeOperationsRef.current.delete(path);
              return;
            }

            // Загружаем предпросмотр с таймаутом
            debugLog(`🎨 Загружаем предпросмотр для: ${path}`);
            const preview: string = await Promise.race([
              invoke<string>("get_image_preview", { filePath: path }),
              new Promise<string>((_, reject) =>
                setTimeout(() => reject(new Error("Preview timeout")), 10000)
              ),
            ]);

            if (!activeOperationsRef.current.has(path)) {
              debugLog(
                `🚫 Операция для ${path} была отменена после загрузки предпросмотра`
              );
              return;
            }

            // Обновляем файл с предпросмотром и статусом
            setFiles((prevFiles) =>
              prevFiles.map((file) =>
                file.path === path
                  ? { ...file, preview, status: "pending" }
                  : file
              )
            );

            debugLog(`✅ Предпросмотр загружен для: ${path}`);
            activeOperationsRef.current.delete(path);
          } catch (error) {
            console.error(`❌ Ошибка обработки файла ${path}:`, error);

            // Обновляем статус на ошибку вместо удаления
            setFiles((prevFiles) =>
              prevFiles.map((file) =>
                file.path === path
                  ? { ...file, status: "error", error: String(error) }
                  : file
              )
            );
            activeOperationsRef.current.delete(path);
          }
        });

        return [...currentFiles, ...newFiles];
      });
    },
    [isLikelyImage]
  );

  const handleConvertOne = useCallback(async (id: number) => {
    console.log(`🚀🚀🚀 handleConvertOne ВЫЗВАНА! ID: ${id}, формат: ${selectedFormat}`);
    debugLog(`🚀 handleConvertOne вызвана для ID: ${id}, формат: ${selectedFormat}`);
    
    // Получаем актуальный формат из состояния, а не из замыкания
    const currentFormat = selectedFormat;
    
    setFiles((currentFiles) => {
      const file = currentFiles.find((f) => f.id === id);
      console.log(`📁 Найден файл:`, file);
      
      if (!file) {
        console.log(`❌ Файл с ID ${id} не найден`);
        return currentFiles;
      }

      // Запускаем конвертацию асинхронно
      (async () => {
        try {
          console.log(`🔄 Начинаем конвертацию файла ${file.originalName} в формат ${currentFormat}`);
          debugLog(`🔄 Начинаем конвертацию файла ${file.originalName} в формат ${currentFormat}`);
          
          let convertedPath: string;
          
          // Вызываем соответствующую функцию в зависимости от формата
          switch (currentFormat) {
            case 'jpeg':
              console.log(`📞 Вызываем convert_to_jpeg для файла: ${file.path}`);
              debugLog(`📞 Вызываем convert_to_jpeg`);
              convertedPath = await invoke<string>("convert_to_jpeg", {
                filePath: file.path,
              });
              break;
            case 'webp':
              console.log(`📞 Вызываем convert_to_webp для файла: ${file.path}`);
              debugLog(`📞 Вызываем convert_to_webp`);
              convertedPath = await invoke<string>("convert_to_webp", {
                filePath: file.path,
              });
              break;
            case 'png':
              console.log(`📞 Вызываем convert_to_png для файла: ${file.path}`);
              debugLog(`📞 Вызываем convert_to_png`);
              convertedPath = await invoke<string>("convert_to_png", {
                filePath: file.path,
              });
              break;
            default:
              throw new Error(`Неподдерживаемый формат: ${currentFormat}`);
          }
          
          console.log(`✅ Конвертация завершена: ${convertedPath}`);
          debugLog(`✅ Конвертация завершена: ${convertedPath}`);
          
          // Извлекаем только имя файла из пути
          const convertedName =
            convertedPath.split(/[\\/]/).pop() || file.originalName;

          setFiles((files) =>
            files.map((f) =>
              f.id === id
                ? {
                    ...f,
                    status: "done",
                    currentName: convertedName,
                    convertedPath: convertedPath,
                  }
                : f
            )
          );
          
          console.log(`🎉 Файл ${file.originalName} успешно конвертирован в ${convertedName}`);
          debugLog(`🎉 Файл ${file.originalName} успешно конвертирован в ${convertedName}`);
        } catch (error) {
          console.error(`❌ Ошибка конвертации файла ${file.originalName}:`, error);
          debugLog(`❌ Ошибка конвертации файла ${file.originalName}:`, error);
          
          setFiles((files) =>
            files.map((f) =>
              f.id === id ? { ...f, status: "error", error: String(error) } : f
            )
          );
        }
      })();

      // Сразу обновляем статус на "converting"
      return currentFiles.map((f) =>
        f.id === id ? { ...f, status: "converting" } : f
      );
    });
  }, [selectedFormat]);

  const handleConvertAll = useCallback(() => {
    console.log(`🚀 handleConvertAll вызвана`);
    debugLog(`🚀 handleConvertAll вызвана`);
    
    setFiles((currentFiles) => {
      const pendingFiles = currentFiles.filter((f) => f.status === "pending");
      console.log(`📁 Найдено файлов для конвертации: ${pendingFiles.length}`);
      debugLog(`📁 Найдено файлов для конвертации: ${pendingFiles.length}`);
      
      pendingFiles.forEach((f) => {
        console.log(`🔄 Запускаем конвертацию для файла ID: ${f.id}`);
        handleConvertOne(f.id);
      });
      return currentFiles;
    });
  }, [handleConvertOne]);

  const removeFile = useCallback((id: number) => {
    setFiles((files) => {
      const fileToRemove = files.find((f) => f.id === id);
      if (fileToRemove) {
        // Отменяем активную операцию для этого файла
        activeOperationsRef.current.delete(fileToRemove.path);
        debugLog(`🗑️ Файл удален: ${fileToRemove.originalName}`);
      }
      return files.filter((f) => f.id !== id);
    });
  }, []);

  const clearAll = useCallback(() => {
    // Отменяем все активные операции
    activeOperationsRef.current.clear();
    setFiles([]);
    debugLog(`🧹 Все файлы очищены`);
  }, []);

  const handleSelectFiles = useCallback(async () => {
    debugLog("🗂️ Открываем диалог выбора файлов...");
    try {
      const selectedPaths: string[] = await invoke<string[]>(
        "open_file_dialog"
      );
      debugLog("📁 Выбранные файлы:", selectedPaths);

      if (selectedPaths.length > 0) {
        await processDroppedFiles(selectedPaths);
      }
    } catch (error) {
      console.error("❌ Ошибка при выборе файлов:", error);
    }
  }, [processDroppedFiles]);

  return {
    files,
    setFiles,
    processDroppedFiles,
    handleConvertOne,
    handleConvertAll,
    removeFile,
    clearAll,
    handleSelectFiles,
  };
};
