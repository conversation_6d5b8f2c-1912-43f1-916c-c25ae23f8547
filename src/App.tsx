import { DropResult } from "@hello-pangea/dnd";
import { useFileProcessing, useDragAndDrop, useWindowDrag, useFormatSelection } from "./hooks";
import { EmptyState, FileList, WindowControls, FormatSelector } from "./components";
import "./App.css";

function App() {
  const { selectedFormat, setSelectedFormat } = useFormatSelection();
  
  const {
    files,
    setFiles,
    processDroppedFiles,
    handleConvertOne,
    handleConvertAll,
    removeFile,
    clearAll,
    handleSelectFiles,
  } = useFileProcessing(selectedFormat);



  const {
    isDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  } = useDragAndDrop({ onFilesDropped: processDroppedFiles });

  useWindowDrag();

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    const reorderedFiles = Array.from(files);
    const [removed] = reorderedFiles.splice(result.source.index, 1);
    reorderedFiles.splice(result.destination.index, 0, removed);

    setFiles(reorderedFiles);
  };

  return (
    <div
      className={`container ${isDragOver ? "drag-over" : ""}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <WindowControls />
      <FormatSelector 
        selectedFormat={selectedFormat}
        onFormatChange={setSelectedFormat}
      />
      {files.length === 0 ? (
        <EmptyState onSelectFiles={handleSelectFiles} />
      ) : (
        <FileList
          files={files}
          onDragEnd={onDragEnd}
          onConvertOne={handleConvertOne}
          onConvertAll={handleConvertAll}
          onRemoveFile={removeFile}
          onClearAll={clearAll}
          onSelectFiles={handleSelectFiles}
          selectedFormat={selectedFormat}
        />
      )}
    </div>
  );
}

export default App;
