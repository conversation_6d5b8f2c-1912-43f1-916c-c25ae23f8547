Превращение идеи и прототипа в структурированный документ с требованиями к продукту (PRD) — ключ к успеху.

Вот подробный и многосторонний PRD для вашего приложения-конвертера. Он основан на вашем запросе, предоставленных интерфейсах и современных практиках разработки на стеке Tauri.

---

### **Документ с требованиями к продукту (PRD): Media Converter**

*   **Версия:** 1.0
*   **Дата:** 01.08.2025 г.
*   **Автор:** Gemini AI
*   **Статус:** Проект

### 1. Введение и Видение Продукта

#### 1.1. Резюме
**Media Converter** — это минималистичное, быстрое и элегантное настольное приложение для macOS, предназначенное для простого и интуитивно понятного преобразования медиафайлов. Начиная с конвертации изображений, продукт стремится стать легковесной, но мощной альтернативой громоздким и сложным профессиональным инструментам, таким как Permute или HandBrake, ориентируясь на повседневные задачи пользователей.

#### 1.2. Проблема
Современные пользователи (контент-мейкеры, веб-разработчики, SMM-специалисты и др.) часто сталкиваются с необходимостью быстро изменить формат изображения или видео. Существующие решения часто либо избыточны по функционалу, либо медленны, либо требуют подписки. Отсутствует простой инструмент "перетащил-конвертировал-получил", который бы органично вписывался в экосистему macOS.

#### 1.3. Целевая аудитория
*   **Веб-разработчики и дизайнеры:** Нуждаются в быстрой конвертации ассетов (например, в JPEG или WebP) для оптимизации веб-страниц.
*   **Контент-креаторы и SMM-менеджеры:** Часто конвертируют изображения для постов в социальных сетях, превью для видео и т.д.
*   **Обычные пользователи:** Хотят уменьшить размер фотографий для отправки по почте или сменить формат файла без необходимости разбираться в сложных настройках.

### 2. Цели и Задачи

| Цель | Метрика Успеха |
| :--- | :--- |
| **Создать MVP-версию продукта** | Выпуск рабочего приложения для macOS (Apple Silicon, Intel), реализующего все функции из раздела "Функциональные требования v1.0". |
| **Обеспечить высокую производительность** | Время запуска приложения < 2 секунд. Конвертация среднего изображения (5 МБ) в JPEG < 1 секунды. Использование ОЗУ в простое < 50 МБ. |
| **Достичь высокого уровня юзабилити** | Пользователь должен интуитивно понимать, как добавить и конвертировать файл без чтения инструкций, основываясь на визуальных подсказках. |

### 3. Функциональные Требования (MVP v1.0)

#### FR-1: Основное окно приложения
*   **FR-1.1:** Приложение должно иметь одно основное окно.
*   **FR-1.2:** Фон окна должен быть полупрозрачным с эффектом размытия (Vibrancy/Blur), аналогичным нативным приложениям macOS.
*   **FR-1.3:** Окно должно иметь стандартные элементы управления macOS (закрыть, свернуть, развернуть).

#### FR-2: Добавление файлов
*   **FR-2.1:** Приложение должно поддерживать добавление файлов путем перетаскивания (Drag-n-Drop) из файловой системы.
*   **FR-2.2:** Зона для перетаскивания должна охватывать всё окно приложения.
*   **FR-2.3:** Приложение должно принимать один или несколько файлов изображений за одно перетаскивание.
*   **FR-2.4:** Поддерживаемые на входе форматы изображений: PNG, JPG/JPEG, BMP, GIF, TIFF. При попытке добавить файл неподдерживаемого типа, он должен быть проигнорирован.

#### FR-3: Список файлов и управление им
*   **FR-3.1:** Добавленные файлы отображаются в виде вертикального списка.
*   **FR-3.2:** Каждый элемент списка должен содержать:
    *   **FR-3.2.1:** Иконку-превью или миниатюру изображения.
    *   **FR-3.2.2:** Имя файла.
    *   **FR-3.2.3:** Текущий статус файла (`Ожидание`, `Конвертация`, `Завершено`, `Ошибка`).
    *   **FR-3.2.4:** Кнопку для удаления файла из списка.
*   **FR-3.3:** В нижней части окна должна быть кнопка "Очистить все" для удаления всех файлов из списка.

#### FR-4: Процесс конвертации
*   **FR-4.1:** **Целевой формат:** Все изображения конвертируются в формат **JPEG**.
*   **FR-4.2:** **Одиночная конвертация:** Рядом с каждым файлом в статусе "Ожидание" должна быть кнопка "Конвертировать".
*   **FR-4.3:** **Пакетная конвертация:** В нижней части окна должна быть кнопка "Конвертировать все", которая запускает процесс для всех файлов в статусе "Ожидание".
*   **FR-4.4:** Процесс конвертации должен быть асинхронным и не блокировать пользовательский интерфейс. Пользователь должен видеть изменение статуса в реальном времени.
*   **FR-4.5:** Сконвертированный файл сохраняется в ту же директорию, где находится исходный файл. Имя нового файла формируется заменой расширения на `.jpeg`. Если файл с таким именем уже существует, он должен быть перезаписан.

#### FR-5: Обратная связь и состояния интерфейса
*   **FR-5.1: Пустое состояние:** Когда список файлов пуст, в центре окна отображается иконка и текст "Перетащите файлы для конвертации" (согласно скриншоту 1).
*   **FR-5.2: Файл добавлен:** Файл появляется в списке со статусом "Ожидание" (согласно скриншоту 2).
*   **FR-5.3: Процесс завершен:** Успешно сконвертированный файл меняет свой статус на "Завершено", иконка-превью меняется на галочку (согласно скриншоту 3), а имя файла в списке обновляется на новое (с расширением `.jpeg`).
*   **FR-5.4: Ошибка:** В случае ошибки конвертации (например, поврежденный файл), статус меняется на "Ошибка" и отображается краткое описание проблемы.

### 4. Нефункциональные Требования

*   **NFR-1: Производительность:** Приложение должно быть нативным и быстрым, используя преимущества Rust для тяжелых вычислений (конвертация) и веб-технологий для гибкости интерфейса.
*   **NFR-2: Платформа:** Основная целевая платформа — **macOS (версии 12 Monterey и новее)**. Сборка должна быть универсальной (Universal Binary) для поддержки архитектур Apple Silicon и Intel.
*   **NFR-3: Безопасность:** Приложение должно запрашивать доступ только к переданным ему файлам и не требовать широких прав доступа к системе. Использование Tauri обеспечивает изоляцию по умолчанию.
*   **NFR-4: Дизайн и UX:** Интерфейс должен быть чистым, минималистичным и соответствовать гайдлайнам Apple Human Interface Guidelines (HIG).

### 5. Пользовательский Интерфейс и Опыт (UI/UX)
Дизайн должен строго следовать предоставленным макетам и общей эстетике "Permute".
*   **Шрифты:** System UI (San Francisco на macOS).
*   **Цветовая палитра:** Темная тема, акцентные цвета для статусов (зеленый для успеха, красный для ошибки).
*   **Анимации:** Плавное добавление/удаление элементов из списка. Индикаторы прогресса (можно в виде заполняющегося круга вокруг иконки) для статуса "Конвертация".

### 6. Технический Стек и Реализация (Рекомендации)

| Компонент | Технология/Библиотека | Версия (на 2024-2025) | Примечания |
| :--- | :--- | :--- | :--- |
| **Основной фреймворк** | Tauri | `~2.0` (beta) или последняя `1.x` | Обеспечивает создание легковесного нативного приложения с помощью Rust и веб-фронтенда. |
| **Бэкенд (Core)** | Rust | `2021 edition` или новее | Для максимальной производительности и безопасности. |
| **Асинхронность (Rust)**| `tokio` | `~1.30` | Стандарт де-факто для асинхронных операций в Rust. Критически важен для неблокирующей конвертации. |
| **Обработка изображений**| `image` (crate) | `~0.25` | Мощная и популярная библиотека для работы с изображениями в Rust. Поддерживает все необходимые форматы. |
| **Фронтенд** | React + TypeScript | React `18.x`, TS `5.x` | Популярный и мощный выбор для создания интерактивных интерфейсов. |
| **Управление состоянием**| Zustand / Jotai | Последние версии | Легковесные и современные решения, которые идеально подходят для приложений такого масштаба, в отличие от более громоздкого Redux. |
| **Стилизация** | CSS Modules / Tailwind CSS | Последние версии | Для инкапсуляции стилей и быстрой разработки UI. |

### 7. Дорожная карта (Roadmap)

*   **v1.0 (MVP - данный PRD):** Конвертация изображений в JPEG.
*   **v1.1 (Ближайшее будущее):**
    *   Добавление целевых форматов: PNG, WebP.
    *   Настройка качества JPEG (слайдер 0-100%).
    *   Реальные миниатюры изображений вместо иконок.
*   **v1.2 (Дальнейшее развитие):**
    *   Поддержка базовой конвертации видео (например, в MP4) с использованием биндингов к FFmpeg.
    *   Создание пресетов (например, "Web-оптимизация", "Для Instagram").
*   **v2.0:**
    *   Расширенные настройки видео/аудио кодеков.
    *   Поддержка Windows.

### 8. Открытые вопросы
*   Нужно ли предупреждать пользователя перед перезаписью существующего файла? (Для v1.0 — нет, упрощаем).
*   Как детально следует отображать текст ошибки пользователю? (Для v1.0 — достаточно общего сообщения "Ошибка конвертации").
*   Требуется ли локализация на другие языки? (Для v1.0 — нет, только русский).

---