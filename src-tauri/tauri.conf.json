{"$schema": "https://schema.tauri.app/config/2", "productName": "permute_converter1", "version": "0.1.0", "identifier": "com.permute_converter1.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Permute Converter", "width": 800, "height": 600, "minWidth": 600, "minHeight": 400, "fullscreen": false, "resizable": true, "transparent": true, "decorations": false}], "security": {"csp": {"default-src": "'self' customprotocol: asset:", "connect-src": "ipc: https://ipc.localhost", "img-src": "'self' asset: https://asset.localhost blob: data:", "style-src": "'unsafe-inline' 'self' asset: https://asset.localhost"}, "capabilities": ["main-capability"]}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}