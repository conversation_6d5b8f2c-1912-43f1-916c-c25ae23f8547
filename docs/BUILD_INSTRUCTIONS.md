# Инструкция по сборке и запуску Permute Converter

## Требования

### macOS
- Node.js (версия 16 или выше)
- Rust (установить через rustup: https://rustup.rs/)
- Xcode Command Line Tools: `xcode-select --install`

## Установка зависимостей

1. Установите Node.js зависимости:
```bash
npm install
```

2. Убедитесь, что Rust установлен:
```bash
rustc --version
cargo --version
```

## Запуск в режиме разработки

```bash
npm run tauri dev
```

Это запустит:
- Vite dev server для фронтенда на http://localhost:1420
- Rust приложение с hot reload

## Сборка для продакшена

```bash
npm run tauri build
```

Собранное приложение будет находиться в:
- `src-tauri/target/release/bundle/macos/`

## Функциональность

### Текущие возможности:
- ✅ Drag & Drop изображений в окно приложения
- ✅ Предпросмотр добавленных изображений
- ✅ Конвертация изображений в JPEG формат
- ✅ Удаление файлов из списка
- ✅ Полупрозрачный интерфейс с размытием (macOS)

### Поддерживаемые форматы:
- Входные: JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP
- Выходной: JPEG

## Использование

1. Запустите приложение
2. Перетащите изображения в окно приложения
3. Нажмите "Конвертировать" для отдельного файла или "Конвертировать все"
4. Конвертированные файлы сохраняются в той же папке с расширением .jpeg

## Устранение неполадок

### Ошибки компиляции Rust
```bash
cd src-tauri
cargo clean
cd ..
npm run tauri dev
```

### Проблемы с зависимостями Node.js
```bash
rm -rf node_modules package-lock.json
npm install
```

### Проблемы с правами доступа к файлам (macOS)
Убедитесь, что приложение имеет права доступа к файлам в System Preferences > Security & Privacy > Privacy > Files and Folders.

## Архитектура

- **Фронтенд**: React + TypeScript + Vite
- **Бэкенд**: Rust + Tauri
- **Обработка изображений**: Rust crate `image`
- **UI**: CSS с backdrop-filter для эффекта размытия

## Файловая структура

```
permute_converter1/
├── src/                    # React фронтенд
│   ├── App.tsx            # Главный компонент
│   ├── App.css            # Стили
│   └── main.tsx           # Точка входа
├── src-tauri/             # Rust бэкенд
│   ├── src/
│   │   └── main.rs        # Главный файл Rust
│   ├── Cargo.toml         # Зависимости Rust
│   └── tauri.conf.json    # Конфигурация Tauri
├── package.json           # Зависимости Node.js
└── vite.config.ts         # Конфигурация Vite
```
