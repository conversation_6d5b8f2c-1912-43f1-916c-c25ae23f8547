Вот подробный и многосторонний документ с требованиями к продукту (PRD) для вашего приложения-конвертера. Этот документ послужит единым источником правды для команды разработки.

---

### **Документ с Требованиями к Продукту (PRD): Project "Simpliform"**

*   **Версия:** 1.0
*   **Дата:** 01.08.2025 г.
*   **Автор:** Gemini AI
*   **Статус:** Утверждено к разработке

---

### **1. Введение и Обзор**

Project "Simpliform" (далее — "Продукт") — это легковесное и интуитивно понятное настольное GUI-приложение для macOS, предназначенное для быстрой конвертации медиафайлов. Продукт разрабатывается на стеке Tauri (React JS + Rust), что обеспечивает высокую производительность, малый размер и нативный внешний вид.

Основная философия продукта — простота и элегантность. Вместо перегруженных интерфейсов и сложных настроек, "Simpliform" предлагает минималистичный, ориентированный на перетаскивание (drag-and-drop) рабочий процесс, вдохновленный эстетикой популярных утилит для macOS, таких как Permute.

Версия 1.0 (MVP) сфокусирована на одной ключевой задаче: конвертации любых растровых изображений в формат JPEG.

### **2. Цели и Задачи**

*   **Цель продукта:** Предоставить пользователям самый быстрый и эстетически приятный способ для выполнения базовых задач по конвертации медиа.
*   **Бизнес-цели:**
    *   Занять нишу "простых" конвертеров с премиальным пользовательским опытом.
    *   Создать основу (фреймворк) для дальнейшего расширения функциональности (новые форматы, типы медиа).
*   **Задачи для MVP (v1.0):**
    *   Реализовать полностью функциональный процесс конвертации изображений в JPEG.
    *   Обеспечить соответствие пользовательского интерфейса предоставленным макетам и требованиям к UX.
    *   Выпустить стабильную сборку приложения для macOS.

### **3. Целевая Аудитория**

1.  **Веб-разработчики и дизайнеры:** Нуждаются в быстром способе оптимизации и приведения изображений к единому формату для веб-сайтов.
2.  **Контент-менеджеры и SMM-специалисты:** Часто работают с изображениями из разных источников и нуждаются в их унификации перед публикацией.
3.  **Обычные пользователи:** Хотят уменьшить размер фотографий для отправки по почте или хранения, не разбираясь в сложных графических редакторах.

### **4. Пользовательские Истории (User Stories)**

*   **Как пользователь,** я хочу перетащить одно или несколько изображений в окно приложения, чтобы быстро добавить их в очередь на конвертацию.
*   **Как пользователь,** я хочу видеть список добавленных файлов с их именами, чтобы понимать, что будет обработано.
*   **Как пользователь,** я хочу иметь возможность запустить конвертацию одного конкретного файла, чтобы не ждать обработки всего списка.
*   **Как пользователь,** я хочу иметь кнопку "Конвертировать все", чтобы запустить пакетную обработку всех ожидающих файлов одним кликом.
*   **Как пользователь,** я хочу видеть четкий индикатор статуса для каждого файла (ожидание, в процессе, завершено, ошибка), чтобы отслеживать прогресс.
*   **Как пользователь,** я хочу, чтобы после успешной конвертации я мог легко понять, что задача выполнена (например, по иконке "галочка").
*   **Как пользователь,** я хочу иметь возможность удалить файл из списка, если я добавил его по ошибке.
*   **Как пользователь,** я хочу иметь возможность очистить весь список, чтобы начать новую сессию работы.
*   **Как пользователь,** я хочу, чтобы приложение имело современный, полупрозрачный дизайн, который гармонично вписывается в экосистему macOS.

### **5. Функциональные Требования**

#### **5.1. Управление файлами**
*   **FR1.1. Добавление файлов:**
    *   Приложение должно поддерживать добавление файлов путем перетаскивания (drag-and-drop) из файловой системы ОС в любое место окна приложения.
    *   Приложение должно фильтровать перетаскиваемые объекты: в список обработки добавляются только файлы, являющиеся изображениями (поддерживаемые форматы: PNG, BMP, GIF, TIFF и др., которые может прочитать библиотека `image`). Другие типы файлов (папки, документы) должны игнорироваться.
*   **FR1.2. Список файлов:**
    *   Добавленные файлы отображаются в виде вертикального списка.
    *   Каждый элемент списка должен содержать:
        *   Иконку-превью (в MVP — статичная иконка, меняющаяся в зависимости от статуса).
        *   Имя файла.
        *   Текущий статус файла: "Ожидание", "Конвертация", "Завершено", "Ошибка".
        *   Кнопку для удаления (`X`).
*   **FR1.3. Удаление файлов:**
    *   Пользователь может удалить отдельный файл из списка, нажав на кнопку "X" рядом с ним.
    *   Пользователь может удалить все файлы из списка, нажав на кнопку "Очистить все".

#### **5.2. Логика Конвертации**
*   **FR2.1. Целевой формат:** Все изображения конвертируются в формат **JPEG**.
*   **FR2.2. Процесс конвертации:**
    *   Конвертация инициируется либо для одного файла (кнопка "Конвертировать" на элементе списка), либо для всех файлов со статусом "Ожидание" (кнопка "Конвертировать все").
    *   Во время конвертации статус файла меняется на "Конвертация".
    *   Исходный файл не изменяется. Новый, сконвертированный файл сохраняется в той же директории, что и исходный файл.
    *   Имя нового файла формируется путем замены расширения исходного файла на `.jpeg`. (Например, `photo.png` -> `photo.jpeg`).
*   **FR2.3. Обратная связь:**
    *   После успешной конвертации статус файла меняется на "Завершено".
    *   В случае ошибки (например, файл поврежден) статус меняется на "Ошибка", и желательно отобразить краткое описание проблемы.

#### **5.3. Пользовательский Интерфейс (UI)**
*   **FR3.1. Начальное состояние (пустой список):**
    *   Окно отображает центральную иконку и текстовое приглашение: "Перетащите файлы для конвертации". (согласно *Изображению 1*).
*   **FR3.2. Состояние с файлами:**
    *   Отображается список добавленных файлов.
    *   В нижней части окна находятся глобальные кнопки управления: "Конвертировать все" и "Очистить все". (согласно *Изображению 2*).
*   **FR3.3. Состояние после конвертации:**
    *   Элемент файла, конвертация которого завершена, визуально изменяется для отображения успеха. В макете это показано большой иконкой "галочки". (согласно *Изображению 3*).

### **6. Нефункциональные Требования**

*   **NFR1. Производительность:**
    *   Интерфейс приложения должен оставаться отзывчивым даже во время конвертации нескольких файлов. Конвертация должна выполняться в фоновом потоке, не блокируя UI.
    *   Запуск приложения должен быть быстрым (менее 2 секунд).
*   **NFR2. Пользовательский Интерфейс и Опыт (UI/UX):**
    *   **Эстетика:** Дизайн должен быть минималистичным, чистым и современным, в духе нативных приложений macOS.
    *   **Прозрачность:** Фон окна приложения должен быть полупрозрачным с эффектом размытия (blur/vibrancy), как это принято в macOS.
    *   **Шрифты:** Использовать системные шрифты macOS (San Francisco) для полной интеграции в среду ОС.
*   **NFR3. Платформа:**
    *   Первоочередная и единственная целевая платформа для MVP — **macOS** (версии 11 Big Sur и новее).
*   **NFR4. Надежность и Обработка Ошибок:**
    *   Приложение не должно "падать" при попытке добавить неподдерживаемый файл или в случае сбоя конвертации. Пользователь должен быть информирован об ошибке.
*   **NFR5. Безопасность:**
    *   Приложение работает только с файлами, явно предоставленными пользователем, и не должно иметь доступа к другим данным в файловой системе.

### **7. Технологический Стек**

*   **Основной фреймворк:** Tauri
*   **Бэкенд:** Rust
    *   **Ключевые библиотеки:** `image` для обработки и конвертации изображений.
*   **Фронтенд:** React JS с TypeScript
    *   **Ключевые библиотеки:** Стандартные хуки React, API Tauri (`@tauri-apps/api`) для взаимодействия с бэкендом и ОС.

### **8. Предположения и Зависимости**

*   Предполагается, что на машине пользователя установлена поддерживаемая версия macOS.
*   Предполагается, что у пользователя есть права на чтение выбранных файлов и на запись в директорию, где они находятся.

### **9. Будущие Улучшения (За рамками MVP v1.0)**

*   Поддержка конвертации в другие форматы (PNG, WebP, HEIC).
*   Добавление настроек конвертации (качество JPEG, изменение размера).
*   Реализация полноценного предпросмотра изображений (thumbnails) в списке.
*   Поддержка конвертации видео- и аудиофайлов.
*   Версии для Windows и Linux.
*   Локализация интерфейса на другие языки.

### **10. Критерии Успеха MVP**

*   Все функциональные и нефункциональные требования, описанные в этом документе, выполнены.
*   Приложение успешно собирается в исполняемый `.app` файл для macOS.
*   Ключевой пользовательский сценарий (drag-drop -> convert -> get JPEG) выполняется без сбоев.
*   Конечный продукт визуально и функционально соответствует предоставленным макетам и описанию.