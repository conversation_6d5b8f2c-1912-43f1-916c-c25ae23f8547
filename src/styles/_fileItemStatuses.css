/* Обновленные стили для file-item с фоновым изображением */
.file-item .preview {
  flex-shrink: 0;
}

/* Статусы с улучшенными иконками */
.file-item.status-done .preview {
  background-color: #4CAF50;
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>');
}

.file-item.status-error .preview {
  background-color: #f44336;
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>');
}

/* Анимация конвертации */
.file-item.status-converting {
  position: relative;
}

.file-item.status-converting::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 122, 255, 0.2),
    transparent
  );
  animation: convertingShine 2s infinite;
  z-index: 2;
  pointer-events: none;
}

.file-item.status-converting .file-item-overlay {
  background-color: rgba(0, 122, 255, 0.05);
}

.file-item.status-converting .preview {
  background-color: #007aff;
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.48.9 6.07 2.38"></path><path d="M12 6v6l4 2"></path></svg>');
  animation: spin 1s linear infinite;
}

@keyframes convertingShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}/* Индикатор загрузки предпросмотра */
.preview.loading {
  background-color: #666;
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.48.9 6.07 2.38"></path><path d="M12 6v6l4 2"></path></svg>');
  animation: spin 1s linear infinite;
}

/* Плавное появление фонового изображения */
.file-item::before {
  transition: opacity 0.3s ease;
}/* 
Статус валидации */
.file-item.status-validating .preview {
  background-color: #ff9800;
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 12l2 2 4-4"></path><path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.48.9 6.07 2.38"></path></svg>');
  animation: spin 1s linear infinite;
}

.file-item.status-validating .file-item-overlay {
  background-color: rgba(255, 152, 0, 0.05);
}
