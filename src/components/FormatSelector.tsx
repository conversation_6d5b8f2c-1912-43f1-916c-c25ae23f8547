import { ConversionFormat, FORMAT_CONFIGS } from '../types/format';
import { useEffect } from 'react';
import './FormatSelector.css';

interface FormatSelectorProps {
  selectedFormat: ConversionFormat;
  onFormatChange: (format: ConversionFormat) => void;
}

export const FormatSelector = ({ selectedFormat, onFormatChange }: FormatSelectorProps) => {
  console.log(`🎨 FormatSelector: selectedFormat =`, selectedFormat);
  const formats: ConversionFormat[] = ['jpeg', 'webp', 'png'];

  // Принудительно применяем стили при изменении формата
  useEffect(() => {
    console.log(`🎨 useEffect: применяем стили для формата ${selectedFormat}`);

    // Убираем активные классы со всех кнопок
    document.querySelectorAll('.format-btn').forEach(btn => {
      btn.classList.remove('active');
      (btn as HTMLElement).style.background = '';
      (btn as HTMLElement).style.borderColor = '';
      (btn as HTMLElement).style.color = '';
      (btn as HTMLElement).style.boxShadow = '';
      (btn as HTMLElement).style.transform = '';
    });

    // Применяем стили к активной кнопке
    const activeBtn = document.querySelector(`[data-format="${selectedFormat}"]`) as HTMLElement;
    if (activeBtn) {
      activeBtn.classList.add('active');

      const colors = {
        jpeg: { bg: 'rgba(220, 53, 69, 0.9)', border: 'rgba(220, 53, 69, 1)', text: 'white', shadow: 'rgba(220, 53, 69, 0.6)' },
        webp: { bg: 'rgba(255, 193, 7, 0.9)', border: 'rgba(255, 193, 7, 1)', text: 'black', shadow: 'rgba(255, 193, 7, 0.6)' },
        png: { bg: 'rgba(0, 123, 255, 0.9)', border: 'rgba(0, 123, 255, 1)', text: 'white', shadow: 'rgba(0, 123, 255, 0.6)' }
      };

      const color = colors[selectedFormat];
      activeBtn.style.background = color.bg;
      activeBtn.style.borderColor = color.border;
      activeBtn.style.color = color.text;
      activeBtn.style.boxShadow = `0 0 15px ${color.shadow}`;
      activeBtn.style.transform = 'scale(1.1)';

      console.log(`🎨 Применили стили к кнопке ${selectedFormat}:`, activeBtn.style.background);
    }
  }, [selectedFormat]);

  return (
    <div className="format-selector">
      {formats.map((format) => {
        const isActive = selectedFormat === format;
        const className = `format-btn ${isActive ? 'active' : ''}`;
        console.log(`🎨 Кнопка ${format}: isActive=${isActive}, className="${className}"`);

        return (
          <button
            key={format}
            className={className}
            onClick={() => {
              console.log(`🎨 КЛИК: Переключаем формат с "${selectedFormat}" на "${format}"`);
              onFormatChange(format);
              // Принудительно обновляем DOM для отладки
              setTimeout(() => {
                const btn = document.querySelector(`[data-format="${format}"]`);
                console.log(`🎨 После клика кнопка ${format}:`, btn?.className, btn?.getAttribute('data-format'));
              }, 100);
            }}
            data-format={format}

          >
            {FORMAT_CONFIGS[format].displayName}
          </button>
        );
      })}
    </div>
  );
};