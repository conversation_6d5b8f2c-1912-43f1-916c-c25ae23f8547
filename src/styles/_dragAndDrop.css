/* Drag & Drop визуальная обратная связь */
.container.drag-over {
  border: 2px dashed #007aff;
  background: rgba(0, 122, 255, 0.1);
}

.container.drag-over .empty-state {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.container.drag-over .drop-icon {
  background-color: #007aff;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
