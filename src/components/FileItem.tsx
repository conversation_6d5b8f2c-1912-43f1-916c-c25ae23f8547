import { Draggable } from "@hello-pangea/dnd";
import { FileItem as FileItemType } from "../types";

interface FileItemProps {
  file: FileItemType;
  index: number;
  onConvert: (id: number) => void;
  onRemove: (id: number) => void;
}

export const FileItem = ({
  file,
  index,
  onConvert,
  onRemove,
}: FileItemProps) => {

  
  return (
    <Draggable key={file.id} draggableId={file.id.toString()} index={index}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`file-item status-${file.status}`}
          style={{
            ...provided.draggableProps.style,
            '--bg-image': file.preview ? `url(${file.preview})` : 'none',
          } as React.CSSProperties & { '--bg-image': string }}
        >
          <div className="file-item-overlay">
            <div className={`preview ${!file.preview ? 'loading' : ''}`}>
              {/* Превью теперь служит только как индикатор статуса */}
            </div>
            <div className="info">
              <p className="name">{file.originalName}</p>
              <p className="status">
                {file.status === "done"
                  ? "Завершено"
                  : file.status === "error"
                  ? `Ошибка: ${file.error}`
                  : file.status === "converting"
                  ? "Конвертация..."
                  : file.status === "validating"
                  ? "Проверка..."
                  : "Ожидание"}
              </p>
              {file.status === "done" && file.currentName !== file.originalName && (
                <p className="converted-name">→ {file.currentName}</p>
              )}
            </div>
            <div className="actions">
              {file.status === "pending" && (
                <button onClick={() => onConvert(file.id)}>Конвертировать</button>
              )}

              <button className="remove-btn" onClick={() => onRemove(file.id)}>
                ×
              </button>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
};
