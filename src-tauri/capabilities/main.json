{"$schema": "https://schema.tauri.app/config/2/capability", "identifier": "main-capability", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "core:event:default", "core:event:allow-listen", "core:event:allow-unlisten", "core:event:allow-emit", "core:window:default", "core:window:allow-set-title", "core:webview:allow-internal-toggle-devtools", "core:app:default", "core:resources:default", "core:menu:default", "core:tray:default", "core:path:default", "core:webview:default", "core:webview:allow-set-webview-focus", "core:window:allow-set-focus", "core:window:allow-set-always-on-top", "dialog:default", "dialog:allow-open"]}