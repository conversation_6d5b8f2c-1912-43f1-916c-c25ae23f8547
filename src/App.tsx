import { useState, useEffect } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { invoke } from '@tauri-apps/api/core';
import './App.css';

interface FileItem {
    id: number;
    path: string;
    name: string;
    status: 'pending' | 'converting' | 'done' | 'error';
    error?: string;
    preview?: string;
}

function App() {
    const [files, setFiles] = useState<FileItem[]>([]);
    const [isDragOver, setIsDragOver] = useState(false);
    const [debugInfo, setDebugInfo] = useState<string[]>([]);

    useEffect(() => {
        console.log('🔧 Настройка слушателей событий...');
        
        // Функция для добавления отладочной информации
        const addDebugInfo = (message: string) => {
            const timestamp = new Date().toLocaleTimeString();
            setDebugInfo(prev => [...prev.slice(-4), `${timestamp}: ${message}`]);
        };

        // Добавляем глобальные обработчики DOM событий для отладки
        const handleDOMDragOver = (e: DragEvent) => {
            console.log('🌐 DOM dragover событие:', e);
            addDebugInfo('🌐 DOM dragover событие получено');
            e.preventDefault();
        };
        
        const handleDOMDrop = (e: DragEvent) => {
            console.log('🌐 DOM drop событие:', e);
            console.log('📁 Files в DOM drop:', e.dataTransfer?.files);
            addDebugInfo(`🌐 DOM drop событие: ${e.dataTransfer?.files?.length || 0} файлов`);
            e.preventDefault();
        };
        
        const handleDOMDragEnter = (e: DragEvent) => {
            console.log('🌐 DOM dragenter событие:', e);
            addDebugInfo('🌐 DOM dragenter событие получено');
            e.preventDefault();
        };
        
        const handleDOMDragLeave = (e: DragEvent) => {
            console.log('🌐 DOM dragleave событие:', e);
            addDebugInfo('🌐 DOM dragleave событие получено');
            e.preventDefault();
        };
        
        // Добавляем обработчики на document
        document.addEventListener('dragover', handleDOMDragOver);
        document.addEventListener('drop', handleDOMDrop);
        document.addEventListener('dragenter', handleDOMDragEnter);
        document.addEventListener('dragleave', handleDOMDragLeave);
        
        const setupListeners = async () => {
            try {
                const window = getCurrentWindow();
                addDebugInfo('🪟 Получили текущее окно');
                
                // Слушатель для file-drop
                console.log('📁 Настраиваем слушатель file-drop...');
                addDebugInfo('📁 Настраиваем слушатель file-drop');
                const unlistenDrop = await window.listen<string[]>('tauri://file-drop', async (event) => {
                    console.log('🎉 ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop!', event);
                    console.log('📋 Payload:', event.payload);
                    addDebugInfo(`🎉 Tauri file-drop: ${event.payload.length} файлов`);
                    
                    const imageFiles: FileItem[] = [];

                    for (let i = 0; i < event.payload.length; i++) {
                        const path = event.payload[i];
                        const name = path.split(/[\\/]/).pop() || 'unknown';
                        console.log(`🔍 Обрабатываем файл ${i + 1}/${event.payload.length}: ${name}`);

                        try {
                            // Проверяем, является ли файл изображением
                            console.log('�️ Праоверяем, является ли файл изображением...');
                            const isImage: boolean = await invoke('is_image_file', { filePath: path });
                            console.log(`✅ Результат проверки для ${name}: ${isImage}`);

                            if (isImage) {
                                // Получаем предпросмотр
                                console.log('🎨 Получаем предпросмотр...');
                                const preview: string = await invoke('get_image_preview', { filePath: path });
                                console.log('✅ Предпросмотр получен');

                                imageFiles.push({
                                    id: Date.now() + i,
                                    path,
                                    name,
                                    status: 'pending',
                                    preview
                                });
                            } else {
                                console.log(`⚠️ Файл ${name} не является изображением`);
                            }
                        } catch (error) {
                            console.error('❌ Ошибка при обработке файла:', error);
                        }
                    }

                    console.log(`📊 Обработано изображений: ${imageFiles.length}`);
                    if (imageFiles.length > 0) {
                        setFiles(prev => {
                            console.log('📝 Добавляем файлы в состояние');
                            return [...prev, ...imageFiles];
                        });
                    }
                });

                // Слушатель для file-drop-hover
                console.log('🎯 Настраиваем слушатель file-drop-hover...');
                addDebugInfo('🎯 Настраиваем слушатель file-drop-hover');
                const unlistenHover = await window.listen('tauri://file-drop-hover', (event) => {
                    console.log('🎯 ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop-hover!', event);
                    addDebugInfo('🎯 ПОЛУЧЕНО СОБЫТИЕ file-drop-hover!');
                    setIsDragOver(true);
                });

                // Слушатель для file-drop-cancelled
                console.log('❌ Настраиваем слушатель file-drop-cancelled...');
                addDebugInfo('❌ Настраиваем слушатель file-drop-cancelled');
                const unlistenCancel = await window.listen('tauri://file-drop-cancelled', (event) => {
                    console.log('❌ ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop-cancelled!', event);
                    addDebugInfo('❌ ПОЛУЧЕНО СОБЫТИЕ file-drop-cancelled!');
                    setIsDragOver(false);
                });

                console.log('✅ Все слушатели событий настроены успешно');

                return () => {
                    console.log('🧹 Очистка слушателей событий...');
                    unlistenDrop();
                    unlistenHover();
                    unlistenCancel();
                    
                    // Удаляем DOM обработчики
                    document.removeEventListener('dragover', handleDOMDragOver);
                    document.removeEventListener('drop', handleDOMDrop);
                    document.removeEventListener('dragenter', handleDOMDragEnter);
                    document.removeEventListener('dragleave', handleDOMDragLeave);
                };
            } catch (error) {
                console.error('❌ Ошибка при настройке слушателей:', error);
                return () => {
                    // Удаляем DOM обработчики даже при ошибке
                    document.removeEventListener('dragover', handleDOMDragOver);
                    document.removeEventListener('drop', handleDOMDrop);
                    document.removeEventListener('dragenter', handleDOMDragEnter);
                    document.removeEventListener('dragleave', handleDOMDragLeave);
                };
            }
        };

        let cleanup: (() => void) | undefined;
        setupListeners().then(cleanupFn => {
            cleanup = cleanupFn;
        }).catch(error => {
            console.error('❌ Ошибка при инициализации слушателей:', error);
        });

        return () => {
            if (cleanup) cleanup();
        };
    }, []);

    const handleConvertOne = async (id: number) => {
        setFiles(files => files.map(f => f.id === id ? { ...f, status: 'converting' } : f));
        const file = files.find(f => f.id === id);
        if (file) {
            try {
                const newName: string = await invoke('convert_to_jpeg', { filePath: file.path });
                setFiles(files => files.map(f =>
                    f.id === id ? { ...f, status: 'done', name: newName } : f
                ));
            } catch (error) {
                 setFiles(files => files.map(f =>
                    f.id === id ? { ...f, status: 'error', error: String(error) } : f
                ));
            }
        }
    };

    const handleConvertAll = () => {
        files.filter(f => f.status === 'pending').forEach(f => handleConvertOne(f.id));
    };

    const removeFile = (id: number) => {
        setFiles(files => files.filter(f => f.id !== id));
    };

    const clearAll = () => {
        setFiles([]);
    };

    const handleSelectFiles = async () => {
        console.log('🗂️ Открываем диалог выбора файлов...');
        try {
            const selectedPaths: string[] = await invoke('open_file_dialog');
            console.log('📁 Выбранные файлы:', selectedPaths);
            
            if (selectedPaths.length > 0) {
                const imageFiles: FileItem[] = [];

                for (let i = 0; i < selectedPaths.length; i++) {
                    const path = selectedPaths[i];
                    const name = path.split(/[\\/]/).pop() || 'unknown';
                    console.log(`🔍 Обрабатываем файл ${i + 1}/${selectedPaths.length}: ${name}`);

                    try {
                        // Проверяем, является ли файл изображением
                        console.log('🖼️ Проверяем, является ли файл изображением...');
                        const isImage: boolean = await invoke('is_image_file', { filePath: path });
                        console.log(`✅ Результат проверки для ${name}: ${isImage}`);

                        if (isImage) {
                            // Получаем предпросмотр
                            console.log('🎨 Получаем предпросмотр...');
                            const preview: string = await invoke('get_image_preview', { filePath: path });
                            console.log('✅ Предпросмотр получен');

                            imageFiles.push({
                                id: Date.now() + i,
                                path,
                                name,
                                status: 'pending',
                                preview
                            });
                        } else {
                            console.log(`⚠️ Файл ${name} не является изображением`);
                        }
                    } catch (error) {
                        console.error('❌ Ошибка при обработке файла:', error);
                    }
                }

                console.log(`📊 Обработано изображений: ${imageFiles.length}`);
                if (imageFiles.length > 0) {
                    setFiles(prev => {
                        console.log('📝 Добавляем файлы в состояние');
                        return [...prev, ...imageFiles];
                    });
                }
            }
        } catch (error) {
            console.error('❌ Ошибка при выборе файлов:', error);
        }
    };

    // Обработчики для визуальной обратной связи drag & drop
    const handleDragEnter = (e: React.DragEvent) => {
        console.log('🎯 handleDragEnter вызван');
        e.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        console.log('👋 handleDragLeave вызван');
        e.preventDefault();
        // Проверяем, что мы действительно покинули контейнер
        if (!e.currentTarget.contains(e.relatedTarget as Node)) {
            setIsDragOver(false);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        console.log('🔄 handleDragOver вызван');
        e.preventDefault();
    };

    const handleDrop = (e: React.DragEvent) => {
        console.log('📦 handleDrop вызван');
        console.log('📋 Drop event:', e);
        console.log('📁 Files in dataTransfer:', e.dataTransfer.files);
        e.preventDefault();
        setIsDragOver(false);
    };

    return (
        <div 
            className={`container ${isDragOver ? 'drag-over' : ''}`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            {/* Отладочная панель */}
            {debugInfo.length > 0 && (
                <div className="debug-panel">
                    <h4>🔧 Отладка Drag & Drop:</h4>
                    {debugInfo.map((info, index) => (
                        <div key={index} className="debug-line">{info}</div>
                    ))}
                </div>
            )}
            
            {files.length === 0 ? (
                <div className="empty-state">
                    <div className="drop-icon"></div>
                    <p>Перетащите изображения сюда для конвертации в JPEG</p>
                    <p className="supported-formats">
                        Поддерживаемые форматы: JPG, PNG, GIF, BMP, TIFF, WEBP
                    </p>
                    <button className="select-files-btn" onClick={handleSelectFiles}>
                        📁 Выбрать файлы
                    </button>
                </div>
            ) : (
                <div className="file-list-container">
                   <div className="file-list">
                       {files.map(file => (
                           <div key={file.id} className={`file-item status-${file.status}`}>
                               <div
                                   className="preview"
                                   style={file.preview ? {
                                       backgroundImage: `url(${file.preview})`,
                                       backgroundSize: 'cover',
                                       backgroundPosition: 'center'
                                   } : {}}
                               ></div>
                               <div className="info">
                                   <p className="name">{file.name}</p>
                                   <p className="status">
                                       {file.status === 'done' ? 'Завершено' :
                                        file.status === 'error' ? `Ошибка: ${file.error}` :
                                        file.status === 'converting' ? 'Конвертация...' : 'Ожидание'}
                                   </p>
                               </div>
                               <div className="actions">
                                   {file.status === 'pending' && (
                                       <button onClick={() => handleConvertOne(file.id)}>
                                           Конвертировать
                                       </button>
                                   )}
                                   <button className="remove-btn" onClick={() => removeFile(file.id)}>×</button>
                               </div>
                           </div>
                       ))}
                   </div>
                    <div className="controls">
                        <button className="add-more-btn" onClick={handleSelectFiles}>
                            ➕ Добавить еще
                        </button>
                        <div className="right-buttons">
                            <button
                                onClick={handleConvertAll}
                                disabled={files.filter(f => f.status === 'pending').length === 0}
                            >
                                Конвертировать все ({files.filter(f => f.status === 'pending').length})
                            </button>
                            <button onClick={clearAll}>Очистить все</button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default App;