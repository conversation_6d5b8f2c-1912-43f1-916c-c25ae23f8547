# Отладка приложения

## Включение режима отладки

### Вариант 1: Аргумент командной строки (для DevTools)
```bash
# В режиме разработки
npm run tauri dev -- --debug

# Для собранного приложения
./target/release/permute_converter1 --debug
```

### Вариант 2: Переменная localStorage (для консольных логов)
Откройте DevTools и выполните в консоли:
```javascript
localStorage.setItem('debug', 'true')
```

Для отключения:
```javascript
localStorage.removeItem('debug')
```

### Вариант 3: Переменная окружения (для разработки)
```bash
# Временно для текущей сессии
export NODE_ENV=development
npm run tauri dev

# Или в .env файле
echo "NODE_ENV=development" > .env
```

## Что включает режим отладки

- **DevTools**: Открываются автоматически при запуске с `--debug`
- **Консольные логи**: Подробные сообщения о drag & drop событиях
- **Отладка файлов**: Информация о обработке изображений
- **События Tauri**: Логирование всех событий перетаскивания

## Отключение отладки

По умолчанию в production режиме отладка отключена. Логи будут показываться только:
- В development режиме (`npm run tauri dev`)
- При установке `localStorage.setItem('debug', 'true')`
- При запуске с аргументом `--debug`