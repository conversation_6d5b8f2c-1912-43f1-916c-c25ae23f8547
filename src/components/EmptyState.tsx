interface EmptyStateProps {
  onSelectFiles: () => void;
}

export const EmptyState = ({ onSelectFiles }: EmptyStateProps) => {
  return (
    <div className="empty-state">
      <div className="drop-icon"></div>
      <p>Перетащите изображения сюда для конвертации в JPEG</p>
      <p className="supported-formats">
        Поддерживаемые форматы: JPG, PNG, GIF, BMP, TIFF, WEBP
      </p>
      <button className="select-files-btn" onClick={onSelectFiles}>
        📁 Выбрать файлы
      </button>
    </div>
  );
};
