import { DragDropContext, Droppable, DropResult } from "@hello-pangea/dnd";
import { FileItem as FileItemType, ConversionFormat } from "../types";
import { FileItem } from "./FileItem";

interface FileListProps {
  files: FileItemType[];
  onDragEnd: (result: DropResult) => void;
  onConvertOne: (id: number) => void;
  onConvertAll: () => void;
  onRemoveFile: (id: number) => void;
  onClearAll: () => void;
  onSelectFiles: () => void;
  selectedFormat: ConversionFormat;
}

export const FileList = ({
  files,
  onDragEnd,
  onConvertOne,
  onConvertAll,
  onRemoveFile,
  onClearAll,
  onSelectFiles,
  selectedFormat,
}: FileListProps) => {
  console.log(`📋 FileList: selectedFormat =`, selectedFormat);
  const pendingFilesCount = files.filter((f) => f.status === "pending").length;

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="files">
        {(provided) => (
          <div
            className="file-list-container"
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            <div className="file-list">
              {files.map((file, index) => (
                <FileItem
                  key={file.id}
                  file={file}
                  index={index}
                  onConvert={onConvertOne}
                  onRemove={onRemoveFile}
                />
              ))}
              {provided.placeholder}
            </div>
            <div className="controls">
              <button className={`add-more-btn format-${selectedFormat}`} onClick={onSelectFiles}>
                ➕ Добавить еще
              </button>
              <div className="right-buttons">
                <button
                  className={`convert-all-btn format-${selectedFormat}`}
                  onClick={onConvertAll}
                  disabled={pendingFilesCount === 0}
                >
                  Конвертировать все ({pendingFilesCount})
                </button>
                <button 
                  className={`clear-all-btn format-${selectedFormat}`}
                  onClick={onClearAll}
                >
                  Очистить все
                </button>
              </div>
            </div>
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};
