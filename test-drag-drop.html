<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag & Drop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .drop-zone {
            width: 400px;
            height: 200px;
            border: 2px dashed #ccc;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .drop-zone.drag-over {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Drag & Drop Test</h1>
    <p>Перетащите файлы в область ниже для тестирования:</p>
    
    <div id="dropZone" class="drop-zone">
        Перетащите файлы сюда
    </div>
    
    <h3>Лог событий:</h3>
    <div id="log" class="log"></div>
    
    <script>
        const dropZone = document.getElementById('dropZone');
        const log = document.getElementById('log');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `${timestamp}: ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }
        
        dropZone.addEventListener('dragenter', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
            addLog('🎯 dragenter событие');
        });
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            addLog('🔄 dragover событие');
        });
        
        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            addLog('👋 dragleave событие');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            addLog(`📦 drop событие - ${e.dataTransfer.files.length} файлов`);
            
            for (let i = 0; i < e.dataTransfer.files.length; i++) {
                const file = e.dataTransfer.files[i];
                addLog(`📁 Файл ${i + 1}: ${file.name} (${file.type})`);
            }
        });
        
        addLog('✅ Тестовая страница загружена');
    </script>
</body>
</html>
