export type ConversionFormat = 'jpeg' | 'webp' | 'png';

export interface FormatConfig {
  name: string;
  extension: string;
  backgroundColor: string;
  displayName: string;
}

export const FORMAT_CONFIGS: Record<ConversionFormat, FormatConfig> = {
  jpeg: {
    name: 'jpeg',
    extension: 'jpg',
    backgroundColor: 'rgba(220, 53, 69, 0.5)', // красный
    displayName: 'JPEG'
  },
  webp: {
    name: 'webp',
    extension: 'webp',
    backgroundColor: 'rgba(255, 193, 7, 0.5)', // желтый
    displayName: 'WebP'
  },
  png: {
    name: 'png',
    extension: 'png',
    backgroundColor: 'rgba(0, 123, 255, 0.5)', // синий
    displayName: 'PNG'
  }
};