import { ConversionFormat, FORMAT_CONFIGS } from '../types/format';
import './FormatSelector.css';

interface FormatSelectorProps {
  selectedFormat: ConversionFormat;
  onFormatChange: (format: ConversionFormat) => void;
}

export const FormatSelector = ({ selectedFormat, onFormatChange }: FormatSelectorProps) => {
  console.log(`🎨 FormatSelector: selectedFormat =`, selectedFormat);
  const formats: ConversionFormat[] = ['jpeg', 'webp', 'png'];

  return (
    <div className="format-selector">
      {formats.map((format) => (
        <button
          key={format}
          className={`format-btn ${selectedFormat === format ? 'active' : ''}`}
          onClick={() => {
            console.log(`🎨 Переключаем формат на:`, format);
            onFormatChange(format);
          }}
          data-format={format}
        >
          {FORMAT_CONFIGS[format].displayName}
        </button>
      ))}
    </div>
  );
};