import { useState, useEffect } from 'react';
import { ConversionFormat, FORMAT_CONFIGS } from '../types/format';

export const useFormatSelection = () => {
  const [selectedFormat, setSelectedFormat] = useState<ConversionFormat>('jpeg');

  // Обновляем CSS переменную для фона при изменении формата
  useEffect(() => {
    const config = FORMAT_CONFIGS[selectedFormat];
    document.documentElement.style.setProperty('--dynamic-background-color', config.backgroundColor);
  }, [selectedFormat]);

  return {
    selectedFormat,
    setSelectedFormat,
    currentConfig: FORMAT_CONFIGS[selectedFormat]
  };
};