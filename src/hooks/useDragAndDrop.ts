import { useState, useEffect } from "react";
import { getCurrentWindow } from "@tauri-apps/api/window";
import { getCurrentWebview } from "@tauri-apps/api/webview";
import { debugLog } from "../utils/debug";

interface UseDragAndDropProps {
  onFilesDropped: (paths: string[]) => Promise<void>;
}

export const useDragAndDrop = ({ onFilesDropped }: UseDragAndDropProps) => {
  const [isDragOver, setIsDragOver] = useState(false);

  useEffect(() => {
    debugLog("🔧 Настройка слушателей событий...");

    // Добавляем глобальные обработчики DOM событий для отладки
    const handleDOMDragOver = (e: DragEvent) => {
      debugLog("🌐 DOM dragover событие:", e);
      e.preventDefault();
    };

    const handleDOMDrop = (e: DragEvent) => {
      debugLog("🌐 DOM drop событие:", e);
      debugLog("📁 Files в DOM drop:", e.dataTransfer?.files);
      e.preventDefault();
    };

    const handleDOMDragEnter = (e: DragEvent) => {
      debugLog("🌐 DOM dragenter событие:", e);
      e.preventDefault();
    };

    const handleDOMDragLeave = (e: DragEvent) => {
      debugLog("🌐 DOM dragleave событие:", e);
      e.preventDefault();
    };

    // Добавляем обработчики на document
    document.addEventListener("dragover", handleDOMDragOver);
    document.addEventListener("drop", handleDOMDrop);
    document.addEventListener("dragenter", handleDOMDragEnter);
    document.addEventListener("dragleave", handleDOMDragLeave);

    const setupListeners = async () => {
      try {
        const window = getCurrentWindow();
        debugLog("🪟 Получили текущее окно");
        const webview = getCurrentWebview();
        debugLog("🌐 Получили текущий webview");

        // Слушатель для file-drop (окно)
        debugLog("📁 Настраиваем слушатель file-drop...");
        const unlistenDrop = await window.listen<string[]>(
          "tauri://file-drop",
          async (event) => {
            debugLog("🎉 ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop!", event);
            debugLog("📋 Payload:", event.payload);
            setIsDragOver(false);
            await onFilesDropped(event.payload);
          }
        );

        // Дополнительный слушатель для события tauri://drag-drop
        debugLog("🎯 Настраиваем слушатель drag-drop...");
        const unlistenDragDrop = await window.listen(
          "tauri://drag-drop",
          async (event: any) => {
            debugLog("🎯 ПОЛУЧЕНО СОБЫТИЕ tauri://drag-drop!", event);

            // Для события tauri://drag-drop paths находятся прямо в payload
            if (event?.payload?.paths && Array.isArray(event.payload.paths)) {
              debugLog("📋 Drag-drop paths:", event.payload.paths);
              setIsDragOver(false);
              await onFilesDropped(event.payload.paths);
            } else {
              debugLog("⚠️ Событие drag-drop без paths:", event);
            }
          }
        );

        // Слушатель для события tauri://drag-over
        debugLog("🎯 Настраиваем слушатель drag-over...");
        const unlistenDragOver = await window.listen(
          "tauri://drag-over",
          (event: any) => {
            debugLog("🎯 ПОЛУЧЕНО СОБЫТИЕ tauri://drag-over!", event);
            setIsDragOver(true);
          }
        );

        // Слушатель для file-drop-hover (окно)
        debugLog("🎯 Настраиваем слушатель file-drop-hover...");
        const unlistenHover = await window.listen(
          "tauri://file-drop-hover",
          (event) => {
            debugLog("🎯 ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop-hover!", event);
            setIsDragOver(true);
          }
        );

        // Слушатель для file-drop-cancelled (окно)
        debugLog("❌ Настраиваем слушатель file-drop-cancelled...");
        const unlistenCancel = await window.listen(
          "tauri://file-drop-cancelled",
          (event) => {
            debugLog("❌ ПОЛУЧЕНО СОБЫТИЕ tauri://file-drop-cancelled!", event);
            setIsDragOver(false);
          }
        );

        // Слушатель drag & drop через Webview API (Tauri v2)
        debugLog("🧲 Настраиваем webview.onDragDropEvent...");
        const unlistenWebviewDnD = await webview.onDragDropEvent(
          async (e: any) => {
            debugLog("🧲 Webview DragDropEvent полное событие:", e);

            // В Tauri 2.0 структура события может быть разной
            const eventType = e?.event || e?.type;

            if (eventType === "over" || eventType === "hover") {
              setIsDragOver(true);
            } else if (
              eventType === "cancel" ||
              eventType === "cancelled" ||
              eventType === "leave"
            ) {
              setIsDragOver(false);
            } else if (eventType === "drop") {
              setIsDragOver(false);

              // Пробуем разные возможные структуры данных
              let paths: string[] = [];

              if (e?.payload?.paths) {
                paths = e.payload.paths;
              } else if (e?.paths) {
                paths = e.paths;
              } else if (Array.isArray(e?.payload)) {
                paths = e.payload;
              } else if (Array.isArray(e)) {
                paths = e;
              }

              debugLog("🧲 Извлеченные пути:", paths);

              if (paths && paths.length > 0) {
                debugLog("🧲 Webview drop paths:", paths);
                await onFilesDropped(paths);
              } else {
                debugLog(
                  "⚠️ Webview drop без paths, полное событие:",
                  JSON.stringify(e, null, 2)
                );
              }
            }
          }
        );

        debugLog("✅ Все слушатели событий настроены успешно");

        return () => {
          debugLog("🧹 Очистка слушателей событий...");
          unlistenDrop();
          unlistenDragDrop();
          unlistenDragOver();
          unlistenHover();
          unlistenCancel();
          unlistenWebviewDnD();

          // Удаляем DOM обработчики
          document.removeEventListener("dragover", handleDOMDragOver);
          document.removeEventListener("drop", handleDOMDrop);
          document.removeEventListener("dragenter", handleDOMDragEnter);
          document.removeEventListener("dragleave", handleDOMDragLeave);
        };
      } catch (error) {
        console.error("❌ Ошибка при настройке слушателей:", error);
        return () => {
          // Удаляем DOM обработчики даже при ошибке
          document.removeEventListener("dragover", handleDOMDragOver);
          document.removeEventListener("drop", handleDOMDrop);
          document.removeEventListener("dragenter", handleDOMDragEnter);
          document.removeEventListener("dragleave", handleDOMDragLeave);
        };
      }
    };

    let cleanup: (() => void) | undefined;
    setupListeners()
      .then((cleanupFn) => {
        cleanup = cleanupFn;
      })
      .catch((error) => {
        console.error("❌ Ошибка при инициализации слушателей:", error);
      });

    return () => {
      if (cleanup) cleanup();
    };
  }, [onFilesDropped]);

  // Обработчики для визуальной обратной связи drag & drop
  const handleDragEnter = (e: React.DragEvent) => {
    debugLog("🎯 handleDragEnter вызван");
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    debugLog("👋 handleDragLeave вызван");
    e.preventDefault();
    // Проверяем, что мы действительно покинули контейнер
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    debugLog("🔄 handleDragOver вызван");
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    debugLog("📦 handleDrop вызван");
    e.preventDefault();
    setIsDragOver(false);
  };

  return {
    isDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  };
};
