:root {
  --background-color: rgba(20, 22, 25, 0.3);
  --border-color: rgba(255, 255, 255, 0.15);
  --text-color: #ffffff;
  --icon-color: #8e8e93;
}

body {
  background: transparent;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  overflow: hidden;
  /* Отключаем стандартное поведение drag & drop */
  -webkit-user-drag: none;
  -webkit-app-region: no-drag;
}

.container {
  background: var(--background-color);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border-radius: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid var(--border-color);
  /* Разрешаем drag & drop для контейнера */
  -webkit-app-region: no-drag;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  user-select: none;
}

.drop-icon {
    width: 80px;
    height: 80px;
    background-color: var(--icon-color);
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>');
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
}

.empty-state p {
    margin-top: 16px;
    font-size: 16px;
    color: var(--text-color);
}

.supported-formats {
    font-size: 14px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    margin-top: 8px !important;
}

.select-files-btn {
    margin-top: 24px;
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.select-files-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.select-files-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
    background: linear-gradient(135deg, #0056CC 0%, #4A47B8 100%);
}

.select-files-btn:hover::before {
    left: 100%;
}

.select-files-btn:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

/* File List */
.file-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.file-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 16px;
}

.file-item {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.file-item .preview {
    width: 50px;
    height: 50px;
    background-color: var(--icon-color);
    border-radius: 4px;
    margin-right: 12px;
    /* Иконка файла */
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
    mask-size: 60%;
    mask-repeat: no-repeat;
    mask-position: center;
}

/* Статусы */
.file-item.status-done .preview {
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="green" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>');
}

.file-item .info {
    flex-grow: 1;
}

.file-item .name {
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

 .file-item .status {
    font-size: 12px;
    color: #a0a0a0;
    margin: 4px 0 0 0;
}

.file-item .actions {
    display: flex;
    align-items: center;
}

button {
    background-color: #333;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #444;
}

button:disabled {
    background-color: #222;
    color: #666;
    cursor: not-allowed;
}

button:disabled:hover {
    background-color: #222;
}

.remove-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #888;
    cursor: pointer;
    margin-left: 10px;
}

/* Панель управления */
.controls {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.controls .add-more-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.controls .add-more-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.controls .right-buttons {
    display: flex;
    gap: 12px;
}

/* Drag & Drop визуальная обратная связь */
.container.drag-over {
    border: 2px dashed #007AFF;
    background: rgba(0, 122, 255, 0.1);
}

.container.drag-over .empty-state {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

.container.drag-over .drop-icon {
    background-color: #007AFF;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Отладочная панель */
.debug-panel {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 10px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    max-width: 300px;
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.debug-panel h4 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 14px;
}

.debug-line {
    margin: 2px 0;
    word-break: break-all;
}