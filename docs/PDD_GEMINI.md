Создание Product Design Document (PDD) — ключевой шаг для согласования видения и обеспечения качественной реализации.

На основе предоставленных требований, изображений и нашего технического решения, я составлю PDD для приложения **"Media Converter"**.

---

### **Product Design Document: Media Converter v1.0**

**Дата:** 01.08.2025 г.
**Версия документа:** 1.0
**Автор:** Gemini AI

---

### **1. Введение и Видение Продукта (Overview & Vision)**

**Продукт "Media Converter"** — это легковесное и интуитивно понятное десктопное приложение для macOS, предназначенное для быстрого и простого преобразования медиафайлов.

**Видение продукта** — создать утилиту, которая станет для пользователя стандартным выбором для базовых операций конвертации. Приложение должно сочетать в себе минималистичный, эстетически привлекательный дизайн в стиле нативных приложений macOS и высокую производительность бэкенда. Оно должно "просто работать", не перегружая пользователя сложными настройками, и быть вдохновлено лучшими практиками утилит вроде Permute.

---

### **2. Цели и Задачи (Goals & Objectives)**

*   **Основная цель:** Создать и выпустить минимально жизнеспособный продукт (MVP), который позволяет пользователям конвертировать любые растровые изображения в формат JPEG.
*   **Цель по UX:** Обеспечить максимально плавный и интуитивный пользовательский опыт, основанный на механике Drag-and-Drop.
*   **Цель по дизайну:** Реализовать современный пользовательский интерфейс с полупрозрачным фоном (эффект "Mica"/"Acrylic") и чистой эстетикой, соответствующей предоставленным макетам.
*   **Техническая цель:** Построить приложение на стеке Tauri (React + Rust) для обеспечения высокой производительности, малого потребления ресурсов и безопасности.

---

### **3. Целевая Аудитория (Target Audience)**

*   **Основные пользователи:** Пользователи macOS (дизайнеры, фотографы-любители, контент-менеджеры, обычные пользователи), которым часто требуется быстро изменить формат изображения для веб, отправки по почте или других задач.
*   **Ключевая потребность:** Нужен инструмент, который не требует долгой установки, изучения и сложных настроек. Процесс должен быть таким же простым, как перетаскивание файла.
*   **Кого мы не рассматриваем (в v1.0):** Профессионалы, которым нужен полный контроль над параметрами кодирования, пакетная обработка с переименованием, работа с RAW-форматами и сложными цветовыми профилями.

---

### **4. Функциональные Требования (Functional Requirements)**

| ID | Требование | Описание | Приоритет |
| :--- | :--- | :--- | :--- |
| **FR-1** | **Прием файлов через Drag-n-Drop** | Пользователь может перетащить один или несколько файлов из Finder или с рабочего стола в окно приложения. | Критический |
| **FR-2** | **Фильтрация и добавление файлов** | Приложение должно проверять, являются ли перетащенные файлы изображениями. Если да, они добавляются в список для обработки. Неподдерживаемые файлы игнорируются. | Критический |
| **FR-3** | **Отображение списка файлов** | Добавленные файлы отображаются в виде списка. Для каждого файла виден его `иконка-плейсхолдер`, `имя файла` и `статус`. | Критический |
| **FR-4** | **Конвертация одиночного файла** | У каждого файла в состоянии "ожидание" есть кнопка для запуска индивидуальной конвертации в JPEG. | Высокий |
| **FR-5** | **Конвертация всех файлов** | В интерфейсе есть общая кнопка "Конвертировать все", которая запускает обработку всех файлов в списке со статусом "ожидание". | Высокий |
| **FR-6** | **Удаление одиночного файла** | Пользователь может удалить любой файл из списка, нажав на соответствующую иконку. | Высокий |
| **FR-7** | **Полная очистка списка** | В интерфейсе есть кнопка "Очистить все", которая удаляет все файлы из списка. | Средний |
| **FR-8** | **Отображение статуса конвертации** | Файл в списке меняет свой статус: `Ожидание` -> `Конвертация` -> `Завершено` / `Ошибка`. | Критический |
| **FR-9** | **Сохранение результата** | Сконвертированный JPEG-файл сохраняется в той же директории, что и исходный файл, с тем же именем, но новым расширением. | Критический |

---

### **5. Нефункциональные Требования (Non-Functional Requirements)**

| ID | Требование | Описание |
| :--- | :--- | :--- |
| **NFR-1**| **Производительность** | Приложение должно быстро запускаться. Процесс конвертации не должен блокировать UI. Потребление CPU/RAM в режиме ожидания должно быть минимальным. |
| **NFR-2**| **Платформа** | Первоочередная целевая платформа — **macOS**. Сборка должна производиться в виде нативного `.app` бандла. |
| **NFR-3**| **Дизайн и эстетика** | Интерфейс должен иметь полупрозрачный фон с эффектом размытия (blur). Все элементы управления должны быть минималистичными и соответствовать макетам. |
| **NFR-4**| **Надежность** | Приложение не должно падать при обработке "битых" или неподдерживаемых файлов; вместо этого должна отображаться ошибка для конкретного файла. |
| **NFR-5**| **Технологический стек** | Бэкенд: **Rust**. Фронтенд: **React (TypeScript)**. Сборка: **Tauri**. |

---

### **6. Дизайн и Пользовательский Интерфейс (UI/UX Design)**

#### **6.1. Визуальный стиль**
*   **Фон:** Полупрозрачный с размытием (Glassmorphism), создающий ощущение глубины и интеграции с рабочим столом macOS.
*   **Цветовая палитра:** Темная тема. Темно-серый фон, белые/светло-серые тексты, акцентные цвета для статусов (зеленый для успеха, красный для ошибки).
*   **Типографика:** Системный шрифт San Francisco для полной нативности.

#### **6.2. Состояния интерфейса (на основе макетов)**

1.  **Пустое состояние / Начальный экран:**
    *   **Описание:** Отображается при запуске приложения или когда список файлов пуст.
    *   **Элементы:** Центральная иконка, символизирующая Drag-and-Drop, и текстовая подсказка "Перетащите файлы для конвертации".
    *   **Цель:** Интуитивно направить пользователя к первому действию.
    *   *Соответствует изображению 3.*

2.  **Состояние с добавленными файлами:**
    *   **Описание:** Появляется после того, как пользователь перетащил один или несколько файлов.
    *   **Элементы:** Список файлов, где каждый элемент содержит иконку-превью, имя файла и статус "Ожидание". Рядом с каждым файлом есть кнопки "Конвертировать" и "Удалить". Внизу окна общие кнопки "Конвертировать все" и "Очистить все".
    *   *Соответствует изображению 2.*

3.  **Состояние после конвертации:**
    *   **Описание:** Показывает результат операции.
    *   **Элементы:** В элементе списка статус меняется на "Завершено", иконка-превью заменяется на иконку успеха (галочка). Кнопка "Конвертировать" для данного файла исчезает.
    *   *Соответствует изображению 1.*

---

### **7. Техническая Архитектура**

Приложение будет использовать гибридную архитектуру Tauri, которая сочетает веб-технологии для UI с нативным бэкендом на Rust.

*   **Фронтенд (React + TypeScript):**
    *   Отвечает за рендеринг всего пользовательского интерфейса.
    *   Управляет состоянием приложения (список файлов, их статусы).
    *   Обрабатывает события пользователя (клики, Drag-and-Drop) через встроенные API Tauri (`listen` для file-drop).
*   **Бэкенд (Rust):**
    *   Содержит основную бизнес-логику.
    *   Реализует команду `convert_to_jpeg`, доступную для вызова из фронтенда.
    *   Использует крейт (библиотеку) `image` для безопасного и эффективного чтения, декодирования и сохранения изображений.
    *   Взаимодействует с файловой системой для чтения исходных и сохранения результирующих файлов.
*   **Коммуникация (Tauri API):**
    *   Фронтенд вызывает бэкенд-функции асинхронно с помощью `invoke('command_name', { args })`.
    *   Это обеспечивает отзывчивость интерфейса, так как ресурсоемкие задачи (конвертация) выполняются в отдельном потоке Rust и не блокируют рендеринг UI.

---

### **8. План Развития (Roadmap)**

#### **Версия 1.0 (MVP):**
*   Реализация всех функциональных и нефункциональных требований, описанных выше.
*   Поддержка конвертации основных растровых форматов (PNG, BMP, TIFF) в JPEG.
*   Стабильная сборка для macOS.

#### **План развития (Post-MVP):**
*   **Расширение форматов:** Добавление поддержки конвертации в PNG, WEBP, HEIC.
*   **Настройки качества:** Возможность выбора качества JPEG (%).
*   **Конвертация видео и аудио:** Добавление поддержки базовых видео- (MP4, MOV) и аудиоформатов (MP3, AAC) с использованием `ffmpeg`.
*   **Реальные превью:** Отображение миниатюр изображений вместо иконок-плейсхолдеров.
*   **Локализация:** Поддержка нескольких языков интерфейса.
*   **Версии для Windows и Linux:** Использование кроссплатформенных возможностей Tauri для сборки под другие ОС.